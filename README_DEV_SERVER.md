# Slicer Local Development Server

This local development server allows you to run and test all the slicer_wsgi_*.py modules on your local machine for frontend redesign work.

## Features

- ✅ Serves all 57+ slicer_wsgi_*.py modules locally
- ✅ Preserves existing functionality and routing
- ✅ Handles authentication and permissions
- ✅ Provides session management
- ✅ Creates local file structure for development
- ✅ Easy start/stop functionality
- ✅ No modifications to existing files

## Quick Start

### Option 1: Using the Manager Script (Recommended)

1. **Test the setup:**
   ```bash
   python3 dev_server_manager.py test
   ```

2. **Start the server:**
   ```bash
   python3 dev_server_manager.py start
   ```

3. **Access the application:**
   - Open your browser to: http://localhost:8000
   - You'll be redirected to the login page

### Option 2: Direct Start

1. **Start the server:**
   ```bash
   python3 start_dev_server.py
   ```

   Or directly:
   ```bash
   python3 local_dev_server.py
   ```

### Login and Use

1. **Login with development credentials:**
   - **developer** / **dev123** (admin user)
   - **admin** / **admin123** (admin user)
   - **test_user** / **test123** (regular user)

2. **Browse available modules:**
   - After login, you'll see a grid of all available slicer modules
   - Click on any module to access it (e.g., `/dashboard`, `/datastore`, `/permissions`)

## File Structure

The development server creates these new files (no existing files are modified):

```
cs_sp_pi_slicer/
├── local_dev_server.py          # Main development server
├── start_dev_server.py          # Simple start script
├── dev_server_manager.py        # Server management script
├── test_dev_server.py           # Test suite
├── local_dev_settings.py        # Local development settings
├── organization.py              # Local organization module
├── permissions.py               # Local permissions module
├── datastore.py                 # Local datastore module
├── login.py                     # Local login module
├── README_DEV_SERVER.md         # This documentation
└── dev_data/                    # Local development data directory
    ├── local_datastore.json     # Local datastore file
    ├── local_sessions.json      # Session storage
    ├── log/slicer/             # Log directories
    ├── shm/                    # Shared memory simulation
    └── www/                    # Web directories
```

## How It Works

### WSGI Dispatcher
The `SlicerWSGIDispatcher` class:
- Automatically discovers all `slicer_wsgi_*.py` files
- Routes requests like `/dashboard` to `slicer_wsgi_dashboard.py`
- Handles authentication and session management
- Provides error handling and debugging

### Local Modules
The server provides local versions of core modules:

- **organization.py**: Provides `get_config()` and session management
- **permissions.py**: Handles user permissions and access control
- **datastore.py**: Simple file-based key-value storage
- **login.py**: Session-based authentication system

### Settings System
- **local_dev_settings.py**: Provides development-specific configuration
- Creates local directory structure in `dev_data/`
- Uses simplified authentication (no LDAP required)

## Server Management Commands

The `dev_server_manager.py` script provides easy server management:

```bash
# Test the setup
python3 dev_server_manager.py test

# Start the server
python3 dev_server_manager.py start

# Stop the server
python3 dev_server_manager.py stop

# Restart the server
python3 dev_server_manager.py restart

# Check server status
python3 dev_server_manager.py status

# Clean development data
python3 dev_server_manager.py clean

# Show help
python3 dev_server_manager.py help
```

## Available Routes

Once the server is running, you can access:

- **/** - Home page with module listing
- **/login** - Login page
- **/logout** - Logout and clear session
- **/dashboard** - Dashboard module
- **/datastore** - Datastore management
- **/permissions** - Permission management
- **/index** - Index module
- **/{module_name}** - Any other slicer_wsgi module

## Development Users

The system comes with three pre-configured users:

| Username | Password | Role | Permissions |
|----------|----------|------|-------------|
| developer | dev123 | Admin | Full access to all modules |
| admin | admin123 | Admin | Full access to all modules |
| test_user | test123 | User | Limited access for testing |

## Configuration

### Changing Port or Host
Edit `local_dev_server.py` and modify:
```python
host = 'localhost'  # Change to '0.0.0.0' for external access
port = 8000         # Change to desired port
```

### Adding Users
Edit `local_dev_settings.py` and add to the `user_list`:
```python
'new_user': {
    'password': 'password123',
    'admin': False,
    'permissions': ['user_', 'dashboard_']
}
```

### Modifying Settings
All development settings are in `local_dev_settings.py`. You can modify:
- File paths
- Authentication settings
- User permissions
- Site title and URLs

## Troubleshooting

### Server Won't Start
- Ensure you're in the correct directory (cs_sp_pi_slicer)
- Check that Python 3 is installed
- Verify all required files are present

### Module Loading Errors
- Check the console output for specific error messages
- Some modules may have dependencies that need to be installed
- Missing modules will be skipped automatically

### Permission Issues
- Use the 'developer' account for full access
- Check the permissions system in `/permissions`
- Grant permissions using the datastore interface

### Port Already in Use
- Change the port in `local_dev_server.py`
- Or kill the process using port 8000:
  ```bash
  lsof -ti:8000 | xargs kill -9
  ```

## Development Workflow

1. **Start the server** with `python start_dev_server.py`
2. **Login** with development credentials
3. **Navigate** to the module you want to work on
4. **Make changes** to the slicer_wsgi_*.py files
5. **Restart the server** to see changes (Ctrl+C then restart)
6. **Test** your changes in the browser

## Stopping the Server

Press `Ctrl+C` in the terminal where the server is running.

## Notes

- The server automatically creates necessary directories
- Session data persists between server restarts
- All data is stored locally in the `dev_data/` directory
- No production data or systems are affected
- The server runs in development mode with detailed error messages
