#!/usr/bin/env python3
"""
Test script to verify the navigation structure matches production requirements
"""

import sys
import os

def test_navigation_structure():
    """Test the navigation structure for different user states"""
    print("Testing Navigation Structure...")
    
    try:
        import slicer_wsgi_index
        from dev import login, permissions
        
        # Test 1: Non-logged-in user (should see public modules)
        print("\n=== Test 1: Non-Logged-In User ===")
        environ_no_user = {
            'REQUEST_METHOD': 'GET',
            'PATH_INFO': '/',
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_COOKIE': '',
            'QUERY_STRING': '',
            'SERVER_NAME': 'localhost',
            'SERVER_PORT': '8000',
            'wsgi.url_scheme': 'http'
        }
        
        body, other = slicer_wsgi_index.make_body(environ_no_user)
        
        # Check for required public modules
        required_public = ['dashboard', 'reports', 'timezones', 'videos']
        for module in required_public:
            if module in body:
                print(f"✅ {module} - visible to non-logged-in users")
            else:
                print(f"❌ {module} - missing for non-logged-in users")
        
        # Should also have login link
        if 'login' in body:
            print("✅ login - available for non-logged-in users")
        else:
            print("❌ login - missing for non-logged-in users")
        
        # Should NOT have logout or user-specific modules
        if 'logout' not in body:
            print("✅ logout - correctly hidden from non-logged-in users")
        if 'datastore' not in body:
            print("✅ datastore - correctly hidden from non-logged-in users")
        
        # Test 2: Logged-in developer user
        print("\n=== Test 2: Logged-In Developer User ===")
        environ_developer = {
            'REQUEST_METHOD': 'GET',
            'PATH_INFO': '/',
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_COOKIE': '',
            'QUERY_STRING': '',
            'SERVER_NAME': 'localhost',
            'SERVER_PORT': '8000',
            'wsgi.url_scheme': 'http'
        }
        
        # Create session for developer
        session_id = login.create_session('developer')
        environ_developer['HTTP_COOKIE'] = f'slicer_session={session_id}'
        
        body, other = slicer_wsgi_index.make_body(environ_developer)
        
        # Should still have public modules
        for module in required_public:
            if module in body:
                print(f"✅ {module} - still visible to logged-in users")
            else:
                print(f"❌ {module} - missing for logged-in users")
        
        # Should have logout and user info
        if 'logout' in body:
            print("✅ logout - available for logged-in users")
        if 'developer' in body:
            print("✅ developer - shows current user")
        
        # Should have admin modules below logout
        admin_modules = ['datastore', 'permissions', 'users', 'organization']
        for module in admin_modules:
            if module in body:
                print(f"✅ {module} - available for admin users")
            else:
                print(f"⚠️  {module} - missing for admin users")
        
        # Test 3: Logged-in test user (limited permissions)
        print("\n=== Test 3: Logged-In Test User ===")
        environ_test_user = {
            'REQUEST_METHOD': 'GET',
            'PATH_INFO': '/',
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_COOKIE': '',
            'QUERY_STRING': '',
            'SERVER_NAME': 'localhost',
            'SERVER_PORT': '8000',
            'wsgi.url_scheme': 'http'
        }
        
        # Create session for test_user
        session_id = login.create_session('test_user')
        environ_test_user['HTTP_COOKIE'] = f'slicer_session={session_id}'
        
        body, other = slicer_wsgi_index.make_body(environ_test_user)
        
        # Should still have public modules
        for module in required_public:
            if module in body:
                print(f"✅ {module} - visible to test user")
            else:
                print(f"❌ {module} - missing for test user")
        
        # Should have logout and user info
        if 'logout' in body:
            print("✅ logout - available for test user")
        if 'test_user' in body:
            print("✅ test_user - shows current user")
        
        # Should have limited modules below logout
        if 'datastore' in body:
            print("✅ datastore - available for test user")
        
        # Should NOT have admin modules
        if 'permissions' not in body:
            print("✅ permissions - correctly hidden from test user")
        if 'users' not in body:
            print("✅ users - correctly hidden from test user")
        
        return True
        
    except Exception as e:
        print(f"❌ Navigation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_navigation_order():
    """Test that navigation appears in the correct order"""
    print("\n=== Testing Navigation Order ===")
    
    try:
        import slicer_wsgi_index
        from dev import login
        
        # Test with logged-in developer
        environ = {
            'REQUEST_METHOD': 'GET',
            'PATH_INFO': '/',
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_COOKIE': '',
            'QUERY_STRING': '',
            'SERVER_NAME': 'localhost',
            'SERVER_PORT': '8000',
            'wsgi.url_scheme': 'http'
        }
        
        session_id = login.create_session('developer')
        environ['HTTP_COOKIE'] = f'slicer_session={session_id}'
        
        body, other = slicer_wsgi_index.make_body(environ)
        
        # Check order: public modules first, then logout, then user-specific modules
        dashboard_pos = body.find('dashboard')
        logout_pos = body.find('logout')
        datastore_pos = body.find('datastore')
        
        if dashboard_pos < logout_pos < datastore_pos:
            print("✅ Navigation order correct: public modules → logout → user modules")
        else:
            print("❌ Navigation order incorrect")
            print(f"   dashboard at {dashboard_pos}, logout at {logout_pos}, datastore at {datastore_pos}")
        
        return True
        
    except Exception as e:
        print(f"❌ Navigation order test failed: {e}")
        return False

def main():
    """Run all navigation tests"""
    print("=" * 60)
    print("Slicer Navigation Structure - Test Suite")
    print("=" * 60)
    
    tests = [
        test_navigation_structure,
        test_navigation_order
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Navigation Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Navigation structure matches production requirements!")
        print("\n✅ Public modules (dashboard, reports, timezones, videos) visible to all users")
        print("✅ Login available for non-logged-in users")
        print("✅ Logout and user info shown for logged-in users")
        print("✅ User-specific modules shown below logout")
        print("✅ Proper permission-based access control")
    else:
        print("❌ Some navigation tests failed.")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
