#!/usr/bin/env python3
"""
Local Development Setup Script for Slicer WSGI Applications

This script prepares the local development environment by:
- Creating symbolic links for shared modules
- Setting up necessary directories
- Validating the setup
- Providing cleanup functionality

Run this script once before starting the local development server.
"""

import os
import sys
import platform
import shutil
import tempfile
import traceback
from pathlib import Path


class LocalSetup:
    def __init__(self):
        self.script_dir = Path(__file__).parent.absolute()
        self.is_windows = platform.system() == 'Windows'
        self.created_links = []
        self.created_dirs = []
        self.errors = []

        # Define the module mappings (target -> source)
        self.module_mappings = {
            'organization.py': 'slicer_wsgi_organization.py',
            'datastore.py': 'slicer_wsgi_datastore.py',
            'permissions.py': 'slicer_wsgi_permissions.py',
            'login.py': 'slicer_wsgi_login.py',
            'settings.py': 'slicer_wsgi_settings_local.py'
        }

        # Directories needed for local development
        # Enhanced to include all directories that organization module might need
        self.required_dirs = [
            'local_dev_data',
            'local_dev_data/slicer',
            'local_dev_data/slicer/upload/files',
            'local_dev_data/slicer/datadrop/json/id',
            'local_dev_data/slicer/checkin/json/id',
            'local_dev_data/slicer/checkin/raw',
            'local_dev_data/slicer/datadrop/raw',
            'local_dev_data/slicer/datadrop/stats/id',
            'local_dev_data/slicer/permissions/raw',
            'local_dev_data/slicer/dashboard/raw',
            'local_dev_data/slicer/scan',
            'local_dev_data/slicer/jamf',
            'local_dev_data/slicer/network/id',
            'local_dev_data/slicer/codeupload/files',
            'local_dev_data/slicer/thirdparty/files',
            'local_dev_data/slicer/multimedia/files',
            'local_dev_data/slicer/tasks',
            'local_dev_data/slicer/watchdog',
            'local_dev_data/logs',
            'local_dev_data/logs/slicer',
            'local_dev_data/logs/slicer/datastore',
            'local_dev_data/logs/slicer/datastorelog',
            'local_dev_data/logs/slicer/datastorelogcache',
            'local_dev_data/logs/slicer/devicecommand',
            'local_dev_data/logs/slicer/apache_logs',
            'local_dev_data/logs/slicer/login',
            'local_dev_data/temp',  # Local equivalent of /dev/shm
            'local_dev_files'  # Local equivalent of /var/www/html files
        ]

        # Essential files that should be created
        self.essential_files = [
            ('local_dev_data/logs/slicer/tasks_run_trust', ''),
            ('local_dev_data/logs/slicer/datastorelogcache/cache.txt', ''),
            ('local_dev_data/logs/slicer/datastore_first_trust_diff', ''),
            ('local_dev_data/temp/datastore_trust', ''),
            ('local_dev_data/temp/dashboard_main_report.json', '{}'),
            ('local_dev_data/slicer/tasks/status_summary', '{}')
        ]

    def log_message(self, message, level="INFO"):
        """Log a message with timestamp and level"""
        print(f"[{level}] {message}")

    def log_error(self, message, exception=None):
        """Log an error message with enhanced details"""
        error_details = message
        if exception:
            error_details += f" - {str(exception)}"
            # Add traceback for debugging
            if hasattr(exception, '__traceback__'):
                import traceback
                tb_lines = traceback.format_exception(type(exception), exception, exception.__traceback__)
                error_details += f"\nTraceback: {''.join(tb_lines[-3:])}"  # Last 3 lines of traceback

        self.errors.append(error_details)
        self.log_message(message, "ERROR")
        if exception:
            self.log_message(f"Exception details: {str(exception)}", "ERROR")

    def create_symlink(self, target, source):
        """Create a symbolic link with cross-platform compatibility"""
        target_path = self.script_dir / target
        source_path = self.script_dir / source

        # Check if source exists
        if not source_path.exists():
            self.log_error(f"Source file not found: {source_path}")
            return False

        # Remove existing target if it exists
        if target_path.exists() or target_path.is_symlink():
            try:
                if target_path.is_symlink():
                    target_path.unlink()
                else:
                    target_path.unlink()
                self.log_message(f"Removed existing file: {target_path}")
            except Exception as e:
                self.log_error(f"Failed to remove existing file {target_path}", e)
                return False

        try:
            if self.is_windows:
                # On Windows, try to create a symbolic link, fall back to copy if needed
                try:
                    target_path.symlink_to(source_path)
                    self.log_message(f"Created symlink: {target} -> {source}")
                except OSError:
                    # Fall back to copying the file
                    shutil.copy2(source_path, target_path)
                    self.log_message(f"Created copy (symlink failed): {target} -> {source}")
            else:
                # On Unix-like systems, create symbolic link
                target_path.symlink_to(source_path)
                self.log_message(f"Created symlink: {target} -> {source}")

            self.created_links.append(target_path)
            return True

        except Exception as e:
            self.log_error(f"Failed to create link {target} -> {source}", e)
            return False

    def create_directory(self, dir_path):
        """Create a directory if it doesn't exist"""
        full_path = self.script_dir / dir_path

        try:
            full_path.mkdir(parents=True, exist_ok=True)
            if full_path not in self.created_dirs:
                self.created_dirs.append(full_path)
                self.log_message(f"Created directory: {dir_path}")
            return True
        except Exception as e:
            self.log_error(f"Failed to create directory {dir_path}", e)
            return False

    def create_essential_files(self):
        """Create essential files that modules might expect"""
        self.log_message("Creating essential files...")
        success = True

        for file_path, content in self.essential_files:
            full_path = self.script_dir / file_path
            try:
                # Ensure parent directory exists
                full_path.parent.mkdir(parents=True, exist_ok=True)

                # Create file if it doesn't exist
                if not full_path.exists():
                    with open(full_path, 'w') as f:
                        f.write(content)
                    self.log_message(f"Created essential file: {file_path}")
                    self.created_links.append(full_path)  # Track for cleanup
                else:
                    self.log_message(f"Essential file already exists: {file_path}")

            except Exception as e:
                self.log_error(f"Failed to create essential file {file_path}", e)
                success = False

        return success

    def create_local_settings(self):
        """Create a local settings file if it doesn't exist"""
        settings_file = self.script_dir / 'local_dev_config.py'

        if settings_file.exists():
            self.log_message("Local settings file already exists")
            return True

        try:
            settings_content = '''"""
Local Development Configuration for Slicer WSGI Applications
This is a fallback configuration file created by setup script.
The main configuration is in local_dev_config.py
"""

def get():
    """Return local development settings"""
    import os
    from pathlib import Path

    script_dir = Path(__file__).parent.absolute()

    return {
        'home_url': 'http://localhost:8000',
        'trust_list': ['localhost', '127.0.0.1', 'admin', 'developer'],
        'login_authentication': {'authentication_type': 'user_list'},
        'days_to_keep': 30,
        'sites_to_drop': {},

        # Local file paths - updated to match local_dev_config structure
        'data_drop_base': str(script_dir / 'local_dev_data' / 'slicer') + '/',
        'ram_disk_path': str(script_dir / 'local_dev_data' / 'temp') + '/',
        'datastore_save_path': str(script_dir / 'local_dev_data' / 'logs' / 'slicer') + '/',

        # Development user list (username: password)
        'user_list': {
            'admin': 'admin123',
            'developer': 'dev123',
            'user': 'user123'
        },

        # Site configuration
        'site_title': 'Slicer 2.0 - Local Development',
        'apache_files_path': str(script_dir / 'local_dev_files'),
        'apache_config_content': '# Local development - no Apache config needed',

        # Development specific settings
        'days_to_keep_dashboard_data': 7,
        'development_mode': True
    }
'''

            with open(settings_file, 'w') as f:
                f.write(settings_content)

            self.created_links.append(settings_file)
            self.log_message("Created local development settings file")
            return True

        except Exception as e:
            self.log_error("Failed to create local settings file", e)
            return False

    def setup_environment_variables(self):
        """Set up environment variables for development mode"""
        self.log_message("Setting up environment variables...")

        try:
            # Set development mode flag
            os.environ['SLICER_DEV_MODE'] = '1'

            # Set project root path
            os.environ['SLICER_PROJECT_ROOT'] = str(self.script_dir)

            # Add project root to Python path if not already there
            current_path = os.environ.get('PYTHONPATH', '')
            if str(self.script_dir) not in current_path:
                if current_path:
                    os.environ['PYTHONPATH'] = f"{self.script_dir}:{current_path}"
                else:
                    os.environ['PYTHONPATH'] = str(self.script_dir)

            # Set other development-specific environment variables
            os.environ['SLICER_DEV_DATA_DIR'] = str(self.script_dir / 'local_dev_data' / 'slicer')
            os.environ['SLICER_DEV_LOG_DIR'] = str(self.script_dir / 'local_dev_data' / 'logs' / 'slicer')
            os.environ['SLICER_DEV_TEMP_DIR'] = str(self.script_dir / 'local_dev_data' / 'temp')

            self.log_message("✓ Environment variables set successfully")
            return True

        except Exception as e:
            self.log_error("Failed to set up environment variables", e)
            return False

    def validate_setup(self):
        """Validate that all required modules can be imported with enhanced error handling"""
        self.log_message("Validating setup...")

        # Set up environment first
        if not self.setup_environment_variables():
            return False

        # Add current directory to Python path for testing
        if str(self.script_dir) not in sys.path:
            sys.path.insert(0, str(self.script_dir))

        validation_errors = []

        # Test importing each module with better error handling
        modules_to_test = list(self.module_mappings.keys())

        # Test local_dev_config first (if it exists)
        local_config_path = self.script_dir / 'local_dev_config.py'
        if local_config_path.exists():
            try:
                import local_dev_config
                # Test that it has the get function
                config = local_dev_config.get()
                if not isinstance(config, dict):
                    validation_errors.append("local_dev_config.get() should return a dict")
                else:
                    self.log_message("✓ Successfully imported and tested local_dev_config")
            except Exception as e:
                validation_errors.append(f"Failed to import local_dev_config: {str(e)}")

        # Test each symlinked module
        for module_file in modules_to_test:
            module_name = module_file.replace('.py', '')
            module_path = self.script_dir / module_file

            # Check if the symlink/file exists first
            if not module_path.exists():
                validation_errors.append(f"Module file missing: {module_file}")
                continue

            try:
                # Clear any cached imports
                if module_name in sys.modules:
                    del sys.modules[module_name]

                # Try to import the module
                imported_module = __import__(module_name)

                # Basic validation - check if it has expected attributes
                if hasattr(imported_module, 'application'):
                    self.log_message(f"✓ Successfully imported {module_name} (WSGI app)")
                elif hasattr(imported_module, 'get'):
                    self.log_message(f"✓ Successfully imported {module_name} (config module)")
                else:
                    self.log_message(f"✓ Successfully imported {module_name}")

            except ImportError as e:
                error_msg = f"Failed to import {module_name}: {str(e)}"
                # Add more context for common import errors
                if "No module named" in str(e):
                    error_msg += " (Check if source file exists and symlink is correct)"
                elif "cannot import name" in str(e):
                    error_msg += " (Check for circular imports or missing dependencies)"
                validation_errors.append(error_msg)
            except Exception as e:
                validation_errors.append(f"Error testing {module_name}: {str(e)}")

        # Test directory access with more detailed checks
        self.log_message("Checking directory structure...")
        for dir_path in self.required_dirs:
            full_path = self.script_dir / dir_path
            if not full_path.exists():
                validation_errors.append(f"Required directory missing: {dir_path}")
            elif not full_path.is_dir():
                validation_errors.append(f"Path exists but is not a directory: {dir_path}")
            elif not os.access(full_path, os.W_OK):
                validation_errors.append(f"Directory not writable: {dir_path}")

        # Test essential files
        self.log_message("Checking essential files...")
        for file_path, _ in self.essential_files:
            full_path = self.script_dir / file_path
            if not full_path.exists():
                validation_errors.append(f"Essential file missing: {file_path}")

        # Test environment variables
        self.log_message("Checking environment variables...")
        required_env_vars = ['SLICER_DEV_MODE', 'SLICER_PROJECT_ROOT']
        for env_var in required_env_vars:
            if not os.environ.get(env_var):
                validation_errors.append(f"Required environment variable not set: {env_var}")

        if validation_errors:
            self.log_error("Validation failed:")
            for error in validation_errors:
                self.log_error(f"  - {error}")

            # Provide helpful suggestions
            self.log_message("\nTroubleshooting suggestions:")
            self.log_message("1. Ensure all source files (slicer_wsgi_*.py) exist")
            self.log_message("2. Check file permissions in the project directory")
            self.log_message("3. Verify that local_dev_config.py was created properly")
            self.log_message("4. Try running 'python setup_local_modules.py cleanup' then 'setup' again")

            return False
        else:
            self.log_message("✓ All validations passed!")
            return True

    def setup(self):
        """Run the complete setup process"""
        self.log_message("Starting local development setup...")
        self.log_message(f"Working directory: {self.script_dir}")
        self.log_message(f"Platform: {platform.system()}")

        success = True

        # Create symbolic links for modules
        self.log_message("\n1. Creating symbolic links for shared modules...")
        for target, source in self.module_mappings.items():
            if not self.create_symlink(target, source):
                success = False

        # Set up environment variables early
        self.log_message("\n2. Setting up environment variables...")
        if not self.setup_environment_variables():
            success = False

        # Create required directories
        self.log_message("\n3. Creating required directories...")
        for dir_path in self.required_dirs:
            if not self.create_directory(dir_path):
                success = False

        # Create essential files
        self.log_message("\n4. Creating essential files...")
        if not self.create_essential_files():
            success = False

        # Create local settings (fallback)
        self.log_message("\n5. Setting up fallback local configuration...")
        if not self.create_local_settings():
            success = False

        # Validate setup
        self.log_message("\n6. Validating setup...")
        if not self.validate_setup():
            success = False

        # Summary
        self.log_message(f"\n{'='*50}")
        if success and not self.errors:
            self.log_message("✓ Setup completed successfully!")
            self.log_message(f"Created {len(self.created_links)} symbolic links")
            self.log_message(f"Created {len(self.created_dirs)} directories")
            self.log_message("\nYou can now run the local development server.")
        else:
            self.log_error("Setup completed with errors:")
            for error in self.errors:
                self.log_error(f"  - {error}")
            self.log_message("\nPlease fix the errors before running the development server.")

        return success and not self.errors

    def cleanup(self):
        """Remove all created links and directories with enhanced cleanup"""
        self.log_message("Cleaning up local development setup...")

        cleanup_errors = []
        removed_count = 0

        # Remove symbolic links and created files
        for link_path in self.created_links:
            try:
                if link_path.exists() or link_path.is_symlink():
                    link_path.unlink()
                    self.log_message(f"Removed: {link_path.name}")
                    removed_count += 1
            except Exception as e:
                cleanup_errors.append(f"Failed to remove {link_path}: {str(e)}")

        # Also remove standard module links that might exist
        standard_modules = ['organization.py', 'datastore.py', 'permissions.py', 'login.py', 'settings.py']
        for module in standard_modules:
            module_path = self.script_dir / module
            try:
                if module_path.exists() or module_path.is_symlink():
                    module_path.unlink()
                    self.log_message(f"Removed: {module}")
                    removed_count += 1
            except Exception as e:
                cleanup_errors.append(f"Failed to remove {module}: {str(e)}")

        # Remove essential files
        for file_path, _ in self.essential_files:
            full_path = self.script_dir / file_path
            try:
                if full_path.exists():
                    full_path.unlink()
                    self.log_message(f"Removed essential file: {file_path}")
                    removed_count += 1
            except Exception as e:
                cleanup_errors.append(f"Failed to remove essential file {file_path}: {str(e)}")

        # Remove created directories (only if empty) - in reverse order
        removed_dirs = 0
        for dir_path in reversed(self.created_dirs):
            try:
                if dir_path.exists() and dir_path.is_dir():
                    # Only remove if directory is empty
                    if not any(dir_path.iterdir()):
                        dir_path.rmdir()
                        self.log_message(f"Removed empty directory: {dir_path.name}")
                        removed_dirs += 1
                    else:
                        self.log_message(f"Skipped non-empty directory: {dir_path.name}")
            except Exception as e:
                cleanup_errors.append(f"Failed to remove directory {dir_path}: {str(e)}")

        # Try to remove main data directories if they're empty
        main_dirs_to_check = ['local_dev_data', 'local_dev_files']
        for main_dir in main_dirs_to_check:
            main_path = self.script_dir / main_dir
            try:
                if main_path.exists() and main_path.is_dir():
                    # Try to remove recursively if empty
                    if not any(main_path.rglob('*')):
                        shutil.rmtree(main_path)
                        self.log_message(f"Removed empty directory tree: {main_dir}")
                        removed_dirs += 1
                    else:
                        self.log_message(f"Skipped non-empty directory tree: {main_dir}")
            except Exception as e:
                cleanup_errors.append(f"Failed to remove directory tree {main_dir}: {str(e)}")

        # Clean up environment variables
        env_vars_to_remove = ['SLICER_DEV_MODE', 'SLICER_PROJECT_ROOT', 'SLICER_DEV_DATA_DIR',
                             'SLICER_DEV_LOG_DIR', 'SLICER_DEV_TEMP_DIR']
        for env_var in env_vars_to_remove:
            if env_var in os.environ:
                del os.environ[env_var]
                self.log_message(f"Removed environment variable: {env_var}")

        # Summary
        self.log_message(f"\nCleanup summary:")
        self.log_message(f"  - Removed {removed_count} files/links")
        self.log_message(f"  - Removed {removed_dirs} directories")

        if cleanup_errors:
            self.log_error("Cleanup completed with errors:")
            for error in cleanup_errors:
                self.log_error(f"  - {error}")
            self.log_message("\nSome files/directories may need manual removal.")
        else:
            self.log_message("✓ Cleanup completed successfully!")

    def status(self):
        """Show the current setup status"""
        self.log_message("Local development setup status:")
        self.log_message(f"Working directory: {self.script_dir}")

        # Check symbolic links
        self.log_message("\nSymbolic links:")
        for target, source in self.module_mappings.items():
            target_path = self.script_dir / target
            source_path = self.script_dir / source

            if target_path.exists():
                if target_path.is_symlink():
                    link_target = target_path.readlink()
                    if link_target.name == source:
                        self.log_message(f"  ✓ {target} -> {source}")
                    else:
                        self.log_message(f"  ⚠ {target} -> {link_target} (expected {source})")
                else:
                    self.log_message(f"  ⚠ {target} exists but is not a symlink")
            else:
                self.log_message(f"  ✗ {target} missing")

        # Check directories
        self.log_message("\nRequired directories:")
        missing_dirs = []
        for dir_path in self.required_dirs:
            full_path = self.script_dir / dir_path
            if full_path.exists():
                self.log_message(f"  ✓ {dir_path}")
            else:
                self.log_message(f"  ✗ {dir_path}")
                missing_dirs.append(dir_path)

        # Check essential files
        self.log_message("\nEssential files:")
        missing_files = []
        for file_path, _ in self.essential_files:
            full_path = self.script_dir / file_path
            if full_path.exists():
                self.log_message(f"  ✓ {file_path}")
            else:
                self.log_message(f"  ✗ {file_path}")
                missing_files.append(file_path)

        # Check local config
        config_file = self.script_dir / 'local_dev_config.py'
        if config_file.exists():
            self.log_message(f"  ✓ local_dev_config.py")
        else:
            self.log_message(f"  ✗ local_dev_config.py")

        # Check environment variables
        self.log_message("\nEnvironment variables:")
        env_vars_to_check = ['SLICER_DEV_MODE', 'SLICER_PROJECT_ROOT']
        missing_env_vars = []
        for env_var in env_vars_to_check:
            if os.environ.get(env_var):
                self.log_message(f"  ✓ {env_var}={os.environ.get(env_var)}")
            else:
                self.log_message(f"  ✗ {env_var}")
                missing_env_vars.append(env_var)

        # Summary
        issues = len(missing_dirs) + len(missing_files) + len(missing_env_vars) + (0 if config_file.exists() else 1)
        if issues > 0:
            self.log_message(f"\n⚠ Setup is incomplete ({issues} issues found). Run 'python setup_local_modules.py setup' to fix.")
        else:
            self.log_message("\n✓ Setup appears to be complete.")


def main():
    """Main entry point"""
    setup = LocalSetup()

    if len(sys.argv) < 2:
        print("Usage: python setup_local_modules.py <command>")
        print("Commands:")
        print("  setup    - Set up the local development environment")
        print("  cleanup  - Remove all created links and directories")
        print("  status   - Show current setup status")
        print("  validate - Validate the current setup")
        sys.exit(1)

    command = sys.argv[1].lower()

    try:
        if command == 'setup':
            success = setup.setup()
            sys.exit(0 if success else 1)
        elif command == 'cleanup':
            setup.cleanup()
        elif command == 'status':
            setup.status()
        elif command == 'validate':
            success = setup.validate_setup()
            sys.exit(0 if success else 1)
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        print(traceback.format_exc())
        sys.exit(1)


if __name__ == '__main__':
    main()
