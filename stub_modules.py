"""
Stub modules for local development of slicer_wsgi applications.

This module provides minimal implementations of organization, datastore, permissions, 
and login modules that allow slicer_wsgi files to run locally without requiring 
the full production environment.

Usage:
    import stub_modules
    stub_modules.inject_stubs()
"""

import os
import sys
import time
import json
from types import ModuleType


def inject_stubs():
    """Inject all stub modules into sys.modules"""
    sys.modules['organization'] = create_organization_stub()
    sys.modules['datastore'] = create_datastore_stub()
    sys.modules['permissions'] = create_permissions_stub()
    sys.modules['login'] = create_login_stub()
    sys.modules['ldapsupport'] = create_ldapsupport_stub()
    sys.modules['rings'] = create_rings_stub()
    sys.modules['scan'] = create_scan_stub()
    sys.modules['tasks'] = create_tasks_stub()
    sys.modules['upload'] = create_upload_stub()
    sys.modules['htmlfiles'] = create_htmlfiles_stub()
    sys.modules['certificates'] = create_certificates_stub()
    sys.modules['watchdog'] = create_watchdog_stub()
    sys.modules['dashboard'] = create_dashboard_stub()
    sys.modules['devicecommand'] = create_devicecommand_stub()


def get_local_dev_path():
    """Get the local development directory path"""
    return os.path.dirname(os.path.abspath(__file__))


def get_local_data_path():
    """Get the local data directory path"""
    local_data = os.path.join(get_local_dev_path(), 'local_dev_data')
    os.makedirs(local_data, exist_ok=True)
    return local_data


def get_local_temp_path():
    """Get the local temp directory path"""
    local_temp = os.path.join(get_local_data_path(), 'temp')
    os.makedirs(local_temp, exist_ok=True)
    return local_temp


def get_local_logs_path():
    """Get the local logs directory path"""
    local_logs = os.path.join(get_local_data_path(), 'logs')
    os.makedirs(local_logs, exist_ok=True)
    return local_logs


def create_organization_stub():
    """Create a stub organization module"""
    module = ModuleType('organization')
    
    def get_config(service):
        """Return mock configuration for any service"""
        base_config = {
            'home_url': 'http://localhost:8000',
            'site_title': 'Slicer 2.0 - Local Development',
            'build': 'local-dev',
            'apache_user_name': 'developer',
            'time_to_remain_valid': 7200,  # 2 hours
            'trust_list': ['developer'],
            'login_authentication': {
                'authentication_type': 'blind_trust',
                'user_domain': '@localhost',
                'user_domain_list': ['@localhost', '@dev.local']
            },
            'user_list': {
                'developer': {'password': 'dev123'},
                'admin': {'password': 'admin123'},
                'test': {'password': 'test123'}
            },
            'days_to_keep': 30,
            'sites_to_drop': {},
            'base_log_path': os.path.join(get_local_logs_path(), 'login') + '/',
            'base_datastore_path': os.path.join(get_local_data_path(), 'datastore') + '/',
            'base_datastorelog_path': os.path.join(get_local_data_path(), 'datastorelog') + '/',
            'base_datastorelog_cache_file': os.path.join(get_local_data_path(), 'datastorelogcache', 'cache.txt'),
            'datastore_first_trust_diff_path': os.path.join(get_local_data_path(), 'datastore_first_trust_diff'),
            'do_trust_path': os.path.join(get_local_temp_path(), 'datastore_trust'),
            'checkin_file_root': os.path.join(get_local_data_path(), 'checkin', 'json', 'id') + '/',
            'datadrop_save_path': os.path.join(get_local_data_path(), 'datadrop', 'json', 'id') + '/',
            'base_upload_path': os.path.join(get_local_data_path(), 'upload', 'files') + '/',
            'tasks_request_path': os.path.join(get_local_data_path(), 'tasks') + '/',
            'time_of_last_tasks_run_trust_path': os.path.join(get_local_data_path(), 'tasks_run_trust'),
        }
        return base_config
    
    def make_all_dirs(config):
        """Create all directories specified in config"""
        for key, value in config.items():
            if isinstance(value, str) and ('/' in value) and not ('://' in value):
                try:
                    if value.endswith('/'):
                        os.makedirs(value, exist_ok=True)
                    else:
                        os.makedirs(os.path.dirname(value), exist_ok=True)
                except:
                    pass
    
    def wrap_page_with_session(environ, html):
        """Add session wrapper to HTML (simplified for development)"""
        if '</body>' in html:
            session_content = '<meta http-equiv="refresh" content="7260">'
            html = html.replace('</body>', session_content + '</body>')
        return html
    
    def make_home_url_from_environ(environ):
        """Build home URL from environment"""
        scheme = environ.get('REQUEST_SCHEME', 'http')
        host = environ.get('HTTP_HOST', 'localhost:8000')
        return f"{scheme}://{host}"
    
    # Attach functions to module
    module.get_config = get_config
    module.make_all_dirs = make_all_dirs
    module.wrap_page_with_session = wrap_page_with_session
    module.make_home_url_from_environ = make_home_url_from_environ
    module.service = "organization"
    module.version = "organization.0.7.dev"
    
    return module


def create_datastore_stub():
    """Create a stub datastore module"""
    module = ModuleType('datastore')
    
    # Mock datastore content
    mock_datastore = {
        'trust_status': 'trusted',
        'service_loader_index_allow_runner': 'yes',
        'service_loader_login_allow_runner': 'yes',
        'service_loader_datastore_allow_runner': 'yes',
        'service_loader_permissions_allow_runner': 'yes',
        'user_login_developer_admin': 'Yes',
        'user_login_developer_details': 'cn,mail,memberOf,displayName',
        'user_login_developer_lastvalidlogin': time.strftime('%Y%m%d_%H'),
        'permissions_developer_development_': 'allow',
        'permissions_developer_admin_': 'allow',
        'permissions_developer_user_': 'allow',
    }
    
    def all_datastore():
        """Return all datastore content"""
        return mock_datastore.copy()
    
    def trust():
        """Return trust status"""
        return True
    
    def get_value(key, default=None):
        """Get a value from the datastore"""
        return mock_datastore.get(key, default)
    
    def set_value(key, value, who='(stub)'):
        """Set a value in the datastore"""
        mock_datastore[key] = value
        return True
    
    # Attach functions to module
    module.all_datastore = all_datastore
    module.trust = trust
    module.get_value = get_value
    module.set_value = set_value
    module.service = "datastore"
    module.version = "datastore.0.5.dev"
    
    return module


def create_permissions_stub():
    """Create a stub permissions module"""
    module = ModuleType('permissions')
    
    def permission_prefix_allowed(environ, prefix):
        """Check if a permission prefix is allowed (always True for development)"""
        return True
    
    def make_permissions_cache_from_datastore(datastore_content):
        """Create permissions cache from datastore"""
        return {
            'developer': {
                'development_': True,
                'admin_': True,
                'user_': True,
            }
        }
    
    def make_module_permissions_for_user_from_permissions_cache(user, permissions_cache, not_allow_list, modules_found_content):
        """Create module permissions for a user"""
        return {
            'current_user': user or 'developer',
            'to_show_above_login': ['index'],
            'to_show_for_login': ['login'],
            'to_show_below_login': [
                'datastore', 'permissions', 'organization', 'dashboard',
                'upload', 'scan', 'tasks', 'rings', 'certificates',
                'watchdog', 'devicecommand', 'htmlfiles'
            ]
        }
    
    def get_module_permissions_for_environ(environ):
        """Get module permissions for environment"""
        return make_module_permissions_for_user_from_permissions_cache(
            'developer', {}, [], ''
        )
    
    def log_page_allowed(environ, service, other):
        """Log page access (no-op for development)"""
        pass
    
    # Attach functions to module
    module.permission_prefix_allowed = permission_prefix_allowed
    module.make_permissions_cache_from_datastore = make_permissions_cache_from_datastore
    module.make_module_permissions_for_user_from_permissions_cache = make_module_permissions_for_user_from_permissions_cache
    module.get_module_permissions_for_environ = get_module_permissions_for_environ
    module.log_page_allowed = log_page_allowed
    module.service = "permissions"
    module.version = "permissions.0.5.dev"
    
    return module


def create_login_stub():
    """Create a stub login module"""
    module = ModuleType('login')
    
    def get_current_user(environ, refresh_timeout=False):
        """Get current user (always return developer for local dev)"""
        return 'developer'
    
    def get_active_users():
        """Get list of active users"""
        return {'developer': [7200]}  # 2 hours remaining
    
    def clean_expired_sessions():
        """Clean expired sessions (no-op for development)"""
        pass
    
    def get_all_login_valid_users():
        """Get all users who have logged in successfully"""
        return ['developer', 'admin', 'test']
    
    def get_all_login_attempt_users():
        """Get all users who have attempted login"""
        return ['developer', 'admin', 'test']
    
    def get_user_is_admin(user):
        """Check if user is admin"""
        return user in ['developer', 'admin']
    
    def set_user_is_admin(user, is_admin=True):
        """Set user admin status (no-op for development)"""
        pass
    
    def set_user_details_keys_string(user, details):
        """Set user details (no-op for development)"""
        pass
    
    # Attach functions to module
    module.get_current_user = get_current_user
    module.get_active_users = get_active_users
    module.clean_expired_sessions = clean_expired_sessions
    module.get_all_login_valid_users = get_all_login_valid_users
    module.get_all_login_attempt_users = get_all_login_attempt_users
    module.get_user_is_admin = get_user_is_admin
    module.set_user_is_admin = set_user_is_admin
    module.set_user_details_keys_string = set_user_details_keys_string
    module.service = "login"
    module.version = "login.0.8.dev"
    
    return module


def create_ldapsupport_stub():
    """Create a stub ldapsupport module"""
    module = ModuleType('ldapsupport')
    
    def get_user_group_member_results(user, domain, password, server, basedn):
        """Mock LDAP group member results"""
        return []
    
    def get_user_details_from_search_results(results):
        """Mock user details from LDAP search"""
        return {
            'cn': 'Developer User',
            'mail': 'developer@localhost',
            'displayName': 'Local Developer',
            'memberOf': 'CN=Developers,OU=Groups,DC=localhost'
        }
    
    # Attach functions to module
    module.get_user_group_member_results = get_user_group_member_results
    module.get_user_details_from_search_results = get_user_details_from_search_results
    module.service = "ldapsupport"
    module.version = "ldapsupport.0.1.dev"
    
    return module


def create_rings_stub():
    """Create a stub rings module"""
    module = ModuleType('rings')
    module.service = "rings"
    module.version = "rings.0.1.dev"
    return module


def create_scan_stub():
    """Create a stub scan module"""
    module = ModuleType('scan')
    module.service = "scan"
    module.version = "scan.0.1.dev"
    return module


def create_tasks_stub():
    """Create a stub tasks module"""
    module = ModuleType('tasks')
    module.service = "tasks"
    module.version = "tasks.0.1.dev"
    return module


def create_upload_stub():
    """Create a stub upload module"""
    module = ModuleType('upload')
    module.service = "upload"
    module.version = "upload.0.1.dev"
    return module


def create_htmlfiles_stub():
    """Create a stub htmlfiles module"""
    module = ModuleType('htmlfiles')
    module.service = "htmlfiles"
    module.version = "htmlfiles.0.1.dev"
    return module


def create_certificates_stub():
    """Create a stub certificates module"""
    module = ModuleType('certificates')
    module.service = "certificates"
    module.version = "certificates.0.1.dev"
    return module


def create_watchdog_stub():
    """Create a stub watchdog module"""
    module = ModuleType('watchdog')
    module.service = "watchdog"
    module.version = "watchdog.0.1.dev"
    return module


def create_dashboard_stub():
    """Create a stub dashboard module"""
    module = ModuleType('dashboard')
    module.service = "dashboard"
    module.version = "dashboard.0.1.dev"
    return module


def create_devicecommand_stub():
    """Create a stub devicecommand module"""
    module = ModuleType('devicecommand')
    module.service = "devicecommand"
    module.version = "devicecommand.0.1.dev"
    return module


# Convenience function for testing
def test_stubs():
    """Test that all stub modules work correctly"""
    inject_stubs()
    
    # Test organization module
    import organization
    config = organization.get_config('test')
    print(f"Organization config keys: {list(config.keys())}")
    
    # Test datastore module
    import datastore
    data = datastore.all_datastore()
    print(f"Datastore trust: {datastore.trust()}")
    print(f"Datastore keys: {list(data.keys())}")
    
    # Test permissions module
    import permissions
    mock_environ = {'REMOTE_ADDR': '127.0.0.1'}
    allowed = permissions.permission_prefix_allowed(mock_environ, 'development_')
    print(f"Permissions allowed: {allowed}")
    
    # Test login module
    import login
    user = login.get_current_user(mock_environ)
    print(f"Current user: {user}")
    
    print("All stub modules working correctly!")


if __name__ == '__main__':
    test_stubs()