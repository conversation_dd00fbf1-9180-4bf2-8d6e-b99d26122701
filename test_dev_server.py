#!/usr/bin/env python3
"""
Test script for the Slicer local development server
"""

import os
import sys
import importlib

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing module imports...")
    
    modules_to_test = [
        'local_dev_settings',
        'organization', 
        'permissions',
        'datastore',
        'login',
        'local_dev_server'
    ]
    
    for module_name in modules_to_test:
        try:
            module = importlib.import_module(module_name)
            print(f"  ✅ {module_name}")
        except ImportError as e:
            print(f"  ❌ {module_name}: {e}")
            return False
    
    return True

def test_wsgi_modules():
    """Test that WSGI modules can be discovered"""
    print("\nTesting WSGI module discovery...")
    
    wsgi_count = 0
    for filename in os.listdir('.'):
        if filename.startswith('slicer_wsgi_') and filename.endswith('.py'):
            module_name = filename[:-3]
            service_name = module_name.replace('slicer_wsgi_', '')
            
            # Skip settings files
            if service_name.startswith('settings_'):
                continue
                
            try:
                module = importlib.import_module(module_name)
                if hasattr(module, 'application'):
                    wsgi_count += 1
                    print(f"  ✅ {service_name}")
                else:
                    print(f"  ⚠️  {service_name} (no application function)")
            except Exception as e:
                print(f"  ❌ {service_name}: {e}")
    
    print(f"\nFound {wsgi_count} valid WSGI modules")
    return wsgi_count > 0

def test_settings():
    """Test settings configuration"""
    print("\nTesting settings configuration...")
    
    try:
        import local_dev_settings
        settings = local_dev_settings.get()
        
        required_keys = ['home_url', 'trust_list', 'user_list', 'login_authentication']
        for key in required_keys:
            if key in settings:
                print(f"  ✅ {key}: {settings[key]}")
            else:
                print(f"  ❌ Missing required key: {key}")
                return False
        
        return True
    except Exception as e:
        print(f"  ❌ Settings error: {e}")
        return False

def test_datastore():
    """Test datastore functionality"""
    print("\nTesting datastore functionality...")
    
    try:
        import datastore
        
        # Test basic operations
        datastore.set_value('test_key', 'test_value')
        value = datastore.get_value('test_key')
        
        if value == 'test_value':
            print("  ✅ Basic set/get operations")
        else:
            print(f"  ❌ Set/get failed: expected 'test_value', got '{value}'")
            return False
        
        # Test trust
        if datastore.trust():
            print("  ✅ Trust function")
        else:
            print("  ❌ Trust function failed")
            return False
        
        return True
    except Exception as e:
        print(f"  ❌ Datastore error: {e}")
        return False

def test_login():
    """Test login functionality"""
    print("\nTesting login functionality...")
    
    try:
        import login
        
        # Test user validation
        if login.validate_login('developer', 'dev123'):
            print("  ✅ User validation (developer)")
        else:
            print("  ❌ User validation failed for developer")
            return False
        
        if not login.validate_login('invalid', 'invalid'):
            print("  ✅ Invalid user rejection")
        else:
            print("  ❌ Invalid user was accepted")
            return False
        
        return True
    except Exception as e:
        print(f"  ❌ Login error: {e}")
        return False

def test_permissions():
    """Test permissions functionality"""
    print("\nTesting permissions functionality...")
    
    try:
        import permissions
        
        # Test permission checking
        if permissions.permission_allowed_user('developer', 'development_'):
            print("  ✅ Developer permissions")
        else:
            print("  ❌ Developer permissions failed")
            return False
        
        return True
    except Exception as e:
        print(f"  ❌ Permissions error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Slicer Local Development Server - Test Suite")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_wsgi_modules,
        test_settings,
        test_datastore,
        test_login,
        test_permissions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The development server is ready to use.")
        print("\nTo start the server, run:")
        print("  python3 start_dev_server.py")
        print("\nThen open your browser to: http://localhost:8000")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
