#!/usr/bin/env python3
"""
Comprehensive test for dashboard page to identify all issues
"""

import sys
import os

def test_dashboard_imports():
    """Test that dashboard can import all required modules"""
    print("=== Testing Dashboard Imports ===")
    
    try:
        import slicer_wsgi_dashboard
        print("✅ Dashboard module imports successfully")
        
        # Check if startup_exceptions has any content
        if hasattr(slicer_wsgi_dashboard, 'startup_exceptions'):
            if slicer_wsgi_dashboard.startup_exceptions:
                print("⚠️  Startup exceptions found:")
                print(slicer_wsgi_dashboard.startup_exceptions)
            else:
                print("✅ No startup exceptions")
        
        # Check if critical variables are defined
        critical_vars = [
            'sites_to_drop',
            'service_config', 
            'dashboard_raw_root',
            's_stash_report_file',
            'days_to_keep_dashboard_data'
        ]
        
        for var in critical_vars:
            if hasattr(slicer_wsgi_dashboard, var):
                value = getattr(slicer_wsgi_dashboard, var)
                print(f"✅ {var}: {type(value).__name__} = {value}")
            else:
                print(f"❌ {var}: NOT DEFINED")
        
        return True
        
    except Exception as e:
        print(f"❌ Dashboard import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dashboard_functionality():
    """Test dashboard page functionality with different scenarios"""
    print("\n=== Testing Dashboard Functionality ===")
    
    try:
        import local_dev_server
        from dev import login
        
        # Create dispatcher
        dispatcher = local_dev_server.SlicerWSGIDispatcher()
        
        # Create session for developer user
        session_id = login.create_session('developer')
        
        # Test scenarios
        test_scenarios = [
            {
                'name': 'Basic GET request',
                'environ': {
                    'REQUEST_METHOD': 'GET',
                    'PATH_INFO': '/dashboard',
                    'REMOTE_ADDR': '127.0.0.1',
                    'HTTP_COOKIE': f'slicer_session={session_id}',
                    'QUERY_STRING': '',
                    'SERVER_NAME': 'localhost',
                    'SERVER_PORT': '8000'
                }
            },
            {
                'name': 'GET with siteid parameter',
                'environ': {
                    'REQUEST_METHOD': 'GET',
                    'PATH_INFO': '/dashboard',
                    'REMOTE_ADDR': '127.0.0.1',
                    'HTTP_COOKIE': f'slicer_session={session_id}',
                    'QUERY_STRING': 'siteid=test_site',
                    'SERVER_NAME': 'localhost',
                    'SERVER_PORT': '8000'
                }
            },
            {
                'name': 'GET with data request',
                'environ': {
                    'REQUEST_METHOD': 'GET',
                    'PATH_INFO': '/dashboard',
                    'REMOTE_ADDR': '127.0.0.1',
                    'HTTP_COOKIE': f'slicer_session={session_id}',
                    'QUERY_STRING': 'get=data&type=issues',
                    'SERVER_NAME': 'localhost',
                    'SERVER_PORT': '8000'
                }
            }
        ]
        
        successful_tests = 0
        total_tests = len(test_scenarios)
        
        for scenario in test_scenarios:
            print(f"\n--- Testing: {scenario['name']} ---")
            
            try:
                responses = []
                def start_response(status, headers):
                    responses.append((status, headers))
                
                result = dispatcher(scenario['environ'], start_response)
                
                if responses:
                    status, headers = responses[0]
                    print(f"Status: {status}")
                    
                    if status.startswith('200'):
                        print(f"✅ {scenario['name']} successful")
                        successful_tests += 1
                        
                        # Get response body
                        try:
                            body = b''.join(result).decode('utf-8')
                            print(f"   Response length: {len(body)} characters")
                            
                            # Check for specific content
                            if 'dashboard' in body.lower():
                                print("   ✅ Contains dashboard content")
                            if 'error' in body.lower():
                                print("   ⚠️  Contains error content")
                            if 'exception' in body.lower():
                                print("   ⚠️  Contains exception content")
                                
                        except Exception as e:
                            print(f"   ⚠️  Could not decode response: {e}")
                            
                    else:
                        print(f"❌ {scenario['name']} failed with status: {status}")
                else:
                    print(f"❌ {scenario['name']} - no response received")
                    
            except Exception as e:
                print(f"❌ {scenario['name']} failed with error: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n=== Dashboard Functionality Results ===")
        print(f"Successful tests: {successful_tests}/{total_tests}")
        
        return successful_tests == total_tests
        
    except Exception as e:
        print(f"❌ Dashboard functionality test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dashboard_direct():
    """Test dashboard module directly to catch specific errors"""
    print("\n=== Testing Dashboard Direct Calls ===")
    
    try:
        import slicer_wsgi_dashboard
        from dev import login
        
        # Create session for developer user
        session_id = login.create_session('developer')
        
        # Create test environment
        environ = {
            'REQUEST_METHOD': 'GET',
            'PATH_INFO': '/dashboard',
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_COOKIE': f'slicer_session={session_id}',
            'QUERY_STRING': '',
            'SERVER_NAME': 'localhost',
            'SERVER_PORT': '8000',
            'REQUEST_SCHEME': 'http',
            'HTTP_HOST': 'localhost:8000',
            'wsgi.url_scheme': 'http',
            'SCRIPT_NAME': '',
            'CONTENT_TYPE': '',
            'CONTENT_LENGTH': '0'
        }
        
        print("Testing make_body function directly...")
        
        try:
            body, other = slicer_wsgi_dashboard.make_body(environ)
            print("✅ make_body executed successfully")
            print(f"   Body length: {len(body)} characters")
            print(f"   Other data: {other}")
            return True
            
        except Exception as e:
            print(f"❌ make_body failed: {e}")
            import traceback
            traceback.print_exc()
            
            # Try to identify the specific error
            error_str = str(e)
            if 'sites_to_drop' in error_str:
                print("🔍 IDENTIFIED ISSUE: sites_to_drop variable not defined")
            if 'NameError' in error_str:
                print("🔍 IDENTIFIED ISSUE: Variable not defined")
            if 'KeyError' in error_str:
                print("🔍 IDENTIFIED ISSUE: Missing key in dictionary/environment")
            
            return False
        
    except Exception as e:
        print(f"❌ Dashboard direct test setup failed: {e}")
        return False

def main():
    """Run all dashboard tests"""
    print("=" * 60)
    print("Dashboard Comprehensive Test Suite")
    print("=" * 60)
    
    tests = [
        test_dashboard_imports,
        test_dashboard_direct,
        test_dashboard_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Dashboard Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Dashboard is working correctly!")
    else:
        print("❌ Dashboard has issues that need to be fixed.")
        print("\nNext steps:")
        print("1. Fix variable definition issues")
        print("2. Handle missing configuration gracefully")
        print("3. Ensure all required modules are available")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
