# Local development permissions module
# Provides simplified permission checking for local development

import datastore
import login

def permission_allowed(environ, page_name):
    """Check if current user has permission for a page"""
    user = login.get_current_user(environ)
    return permission_allowed_user(user, page_name)

def permission_allowed_user(user, page_name, data_store_content=None, allow_overrides=True):
    """Check if a specific user has permission for a page"""
    if not user:
        return False
    
    # Get user settings
    try:
        import local_dev_settings as settings
        user_list = settings.get().get('user_list', {})
        trust_list = settings.get().get('trust_list', [])
    except:
        user_list = {}
        trust_list = ['developer', 'admin']
    
    # Check if user is in trust list (always has permissions)
    if user in trust_list:
        return True
    
    # Check if user is admin
    admin_key = f'user_login_{user}_admin'
    is_admin = datastore.get_value(admin_key, 'No') == 'Yes'
    
    if is_admin:
        return True
    
    # Check user-specific permissions from settings
    if user in user_list:
        user_permissions = user_list[user].get('permissions', [])
        for perm in user_permissions:
            if page_name.startswith(perm):
                return True
    
    # Check datastore permissions
    permission_key = f'permissions_{user}_{page_name}'
    permission_value = datastore.get_value(permission_key, 'deny')
    
    return permission_value == 'allow'

def permission_prefix_allowed(environ, prefix_name):
    """Check if current user has permission for a prefix"""
    user = login.get_current_user(environ)
    return permission_prefix_allowed_user(user, prefix_name)

def permission_prefix_allowed_user(user, prefix_name):
    """Check if a specific user has permission for a prefix"""
    if not user:
        return False
    
    # Get user settings
    try:
        import local_dev_settings as settings
        user_list = settings.get().get('user_list', {})
        trust_list = settings.get().get('trust_list', [])
    except:
        user_list = {}
        trust_list = ['developer', 'admin']
    
    # Check if user is in trust list (always has permissions)
    if user in trust_list:
        return True
    
    # Check if user is admin
    admin_key = f'user_login_{user}_admin'
    is_admin = datastore.get_value(admin_key, 'No') == 'Yes'
    
    if is_admin:
        return True
    
    # Check user-specific permissions from settings
    if user in user_list:
        user_permissions = user_list[user].get('permissions', [])
        for perm in user_permissions:
            if prefix_name.startswith(perm) or perm.startswith(prefix_name):
                return True
    
    return False

def set_permission_allowed(user, page_name, value_to_set):
    """Set permission for a user and page"""
    permission_key = f'permissions_{user}_{page_name}'
    datastore.set_value(permission_key, value_to_set)

def set_permission_override(page_name, value_to_set):
    """Set permission override for a page"""
    override_key = f'override_permission_{page_name}'
    datastore.set_value(override_key, value_to_set)

def get_permission_override(page_name):
    """Get permission override for a page"""
    override_key = f'override_permission_{page_name}'
    return datastore.get_value(override_key, 'None')

def get_all_permissions():
    """Get all permission keys"""
    all_keys = datastore.get_all_keys()
    permission_keys = []
    
    for key in all_keys:
        if key.startswith('permissions_'):
            # Extract permission name from key like 'permissions_user_page_name'
            parts = key.split('_', 2)
            if len(parts) >= 3:
                permission_keys.append(parts[2])
    
    return permission_keys

def log_page_allowed(environ, service, other):
    """Log page access (simplified for development)"""
    user = login.get_current_user(environ)
    print(f"[PERMISSIONS] User '{user}' accessed service '{service}' - Status: {other.get('status', 'Unknown')}")

def get_modules_with_overrides(override_type='Login'):
    """Get modules with permission overrides"""
    # For development, return empty list
    return []

# Development helper functions
def grant_all_permissions(user):
    """Grant all permissions to a user (development helper)"""
    common_permissions = [
        'development_', 'admin_', 'user_', 'dashboard_', 'datastore_', 
        'permissions_', 'login_', 'index_', 'organization_'
    ]
    
    for perm in common_permissions:
        set_permission_allowed(user, perm, 'allow')
    
    # Set admin status
    admin_key = f'user_login_{user}_admin'
    datastore.set_value(admin_key, 'Yes')

# Initialize default permissions for development users
def _init_default_permissions():
    """Initialize default permissions for development"""
    grant_all_permissions('developer')
    grant_all_permissions('admin')
    
    # Basic permissions for test_user
    set_permission_allowed('test_user', 'user_', 'allow')
    set_permission_allowed('test_user', 'dashboard_', 'allow')
    set_permission_allowed('test_user', 'datastore_', 'allow')

# Initialize on import
_init_default_permissions()
