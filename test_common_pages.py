#!/usr/bin/env python3
"""
Test script to verify that common pages work correctly with the environment variable fix
"""

import sys
import os

def test_common_pages():
    """Test that common pages load without environment variable errors"""
    print("Testing common pages with environment variable fix...")
    
    try:
        import local_dev_server
        from dev import login
        
        # Create the dispatcher
        dispatcher = local_dev_server.SlicerWSGIDispatcher()
        
        # Create a session for developer user
        session_id = login.create_session('developer')
        
        # Base environment
        base_environ = {
            'REQUEST_METHOD': 'GET',
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_COOKIE': f'slicer_session={session_id}',
            'QUERY_STRING': '',
            'SERVER_NAME': 'localhost',
            'SERVER_PORT': '8000'
        }
        
        # Pages to test
        pages_to_test = [
            ('index', 'Index page'),
            ('dashboard', 'Dashboard'),
            ('permissions', 'Permissions'),
            ('datastore', 'Datastore'),
            ('reports', 'Reports'),
            ('timezones', 'Timezones'),
            ('videos', 'Videos'),
            ('users', 'Users'),
            ('organization', 'Organization')
        ]
        
        successful_pages = 0
        total_pages = len(pages_to_test)
        
        for page_path, page_name in pages_to_test:
            print(f"\n--- Testing {page_name} ({page_path}) ---")
            
            # Create environment for this page
            environ = base_environ.copy()
            environ['PATH_INFO'] = f'/{page_path}'
            
            try:
                responses = []
                def start_response(status, headers):
                    responses.append((status, headers))
                
                result = dispatcher(environ, start_response)
                
                if responses:
                    status, headers = responses[0]
                    print(f"Status: {status}")
                    
                    if status.startswith('200'):
                        print(f"✅ {page_name} loads successfully")
                        successful_pages += 1
                        
                        # Get response body to check content
                        try:
                            body = b''.join(result).decode('utf-8')
                            if len(body) > 100:
                                print(f"   Content length: {len(body)} characters")
                            else:
                                print(f"   ⚠️  Short content: {len(body)} characters")
                        except:
                            print("   Content check failed")
                            
                    elif status.startswith('302'):
                        print(f"✅ {page_name} redirects (likely to login)")
                        successful_pages += 1
                    else:
                        print(f"❌ {page_name} failed with status: {status}")
                else:
                    print(f"❌ {page_name} - no response received")
                    
            except Exception as e:
                print(f"❌ {page_name} failed with error: {e}")
        
        print(f"\n=== Results ===")
        print(f"Successful pages: {successful_pages}/{total_pages}")
        
        if successful_pages == total_pages:
            print("🎉 All common pages work correctly!")
            return True
        else:
            print("❌ Some pages failed to load")
            return False
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_variables():
    """Test that environment variables are properly added"""
    print("\nTesting environment variable handling...")
    
    try:
        import local_dev_server
        
        # Create dispatcher
        dispatcher = local_dev_server.SlicerWSGIDispatcher()
        
        # Test with minimal environment
        minimal_environ = {
            'REQUEST_METHOD': 'GET',
            'PATH_INFO': '/dashboard',
            'SERVER_NAME': 'localhost',
            'SERVER_PORT': '8000'
        }
        
        # The dispatcher should add missing variables
        dispatcher._add_missing_environ_vars(minimal_environ)
        
        # Check that required variables were added
        required_vars = [
            'REQUEST_SCHEME',
            'HTTP_HOST', 
            'wsgi.url_scheme',
            'SCRIPT_NAME',
            'CONTENT_TYPE',
            'CONTENT_LENGTH',
            'QUERY_STRING',
            'HTTP_USER_AGENT',
            'HTTP_ACCEPT'
        ]
        
        missing_vars = []
        for var in required_vars:
            if var not in minimal_environ:
                missing_vars.append(var)
            else:
                print(f"✅ {var}: {minimal_environ[var]}")
        
        if not missing_vars:
            print("✅ All required environment variables added")
            return True
        else:
            print(f"❌ Missing variables: {missing_vars}")
            return False
            
    except Exception as e:
        print(f"❌ Environment variable test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Common Pages Test Suite")
    print("=" * 60)
    
    tests = [
        test_environment_variables,
        test_common_pages
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All common pages are working correctly!")
        print("\n✅ Environment variable fix successful")
        print("✅ Dashboard, permissions, and other pages load without errors")
        print("✅ Ready for frontend redesign work")
    else:
        print("❌ Some tests failed.")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
