#!/usr/bin/env python3
"""
Cleanup Development Files Script

This script cleans up all files and directories created by the complex local development setup,
preparing the repository for the simple server approach.

Usage:
    python3 cleanup_dev_files.py [--dry-run] [--force] [--verbose]

Options:
    --dry-run    Show what would be deleted without actually deleting
    --force      Skip confirmation prompts
    --verbose    Show detailed output
"""

import os
import sys
import shutil
import argparse
from pathlib import Path


class DevFilesCleaner:
    def __init__(self, dry_run=False, force=False, verbose=False):
        self.script_dir = Path(__file__).parent.absolute()
        self.dry_run = dry_run
        self.force = force
        self.verbose = verbose
        self.items_to_remove = []
        self.errors = []
        self.removed_count = 0
        
        # Define all items that should be cleaned up based on setup_local_modules.py
        self.symlinks_to_remove = [
            'organization.py',
            'datastore.py', 
            'permissions.py',
            'login.py',
            'settings.py'
        ]
        
        # Directories created by the complex setup
        self.directories_to_remove = [
            'local_dev_data',
            'local_dev_files',
            'local_data',  # Alternative name that might exist
            'local_temp'   # Alternative name that might exist
        ]
        
        # Configuration files created by setup
        self.config_files_to_remove = [
            'local_dev_config.py',
            '__pycache__'  # Python cache directory
        ]
        
        # Environment variables to clean up
        self.env_vars_to_clean = [
            'SLICER_DEV_MODE',
            'SLICER_PROJECT_ROOT', 
            'SLICER_DEV_DATA_DIR',
            'SLICER_DEV_LOG_DIR',
            'SLICER_DEV_TEMP_DIR'
        ]

    def log_message(self, message, level="INFO"):
        """Log a message with appropriate formatting"""
        if level == "ERROR":
            print(f"❌ {message}")
        elif level == "WARNING":
            print(f"⚠️  {message}")
        elif level == "SUCCESS":
            print(f"✅ {message}")
        elif self.verbose or level == "INFO":
            print(f"ℹ️  {message}")

    def log_error(self, message, exception=None):
        """Log an error and add to error list"""
        error_msg = message
        if exception:
            error_msg += f" - {str(exception)}"
        self.errors.append(error_msg)
        self.log_message(error_msg, "ERROR")

    def scan_for_cleanup_items(self):
        """Scan the directory for items that need cleanup"""
        self.log_message("Scanning for cleanup items...")
        
        # Check for symbolic links
        for symlink in self.symlinks_to_remove:
            symlink_path = self.script_dir / symlink
            if symlink_path.exists() or symlink_path.is_symlink():
                item_type = "symlink" if symlink_path.is_symlink() else "file"
                self.items_to_remove.append({
                    'path': symlink_path,
                    'type': item_type,
                    'category': 'Symbolic Links'
                })
        
        # Check for directories
        for directory in self.directories_to_remove:
            dir_path = self.script_dir / directory
            if dir_path.exists() and dir_path.is_dir():
                # Calculate directory size for display
                try:
                    size = sum(f.stat().st_size for f in dir_path.rglob('*') if f.is_file())
                    file_count = len(list(dir_path.rglob('*')))
                    self.items_to_remove.append({
                        'path': dir_path,
                        'type': 'directory',
                        'category': 'Development Directories',
                        'size': size,
                        'file_count': file_count
                    })
                except Exception as e:
                    self.items_to_remove.append({
                        'path': dir_path,
                        'type': 'directory',
                        'category': 'Development Directories',
                        'size': 0,
                        'file_count': 0,
                        'scan_error': str(e)
                    })
        
        # Check for config files
        for config_file in self.config_files_to_remove:
            config_path = self.script_dir / config_file
            if config_path.exists():
                item_type = "directory" if config_path.is_dir() else "file"
                self.items_to_remove.append({
                    'path': config_path,
                    'type': item_type,
                    'category': 'Configuration Files'
                })
        
        # Check for Python cache files
        pycache_dirs = list(self.script_dir.rglob('__pycache__'))
        for pycache_dir in pycache_dirs:
            self.items_to_remove.append({
                'path': pycache_dir,
                'type': 'directory',
                'category': 'Python Cache'
            })
        
        # Check for .pyc files
        pyc_files = list(self.script_dir.rglob('*.pyc'))
        for pyc_file in pyc_files:
            self.items_to_remove.append({
                'path': pyc_file,
                'type': 'file',
                'category': 'Python Cache'
            })

    def format_size(self, size_bytes):
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"

    def display_cleanup_plan(self):
        """Display what will be cleaned up"""
        if not self.items_to_remove:
            self.log_message("No cleanup items found. Repository appears to be clean.", "SUCCESS")
            return False
        
        print(f"\n{'='*60}")
        print(f"CLEANUP PLAN - {len(self.items_to_remove)} items found")
        print(f"{'='*60}")
        
        # Group items by category
        categories = {}
        for item in self.items_to_remove:
            category = item['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(item)
        
        total_size = 0
        total_files = 0
        
        for category, items in categories.items():
            print(f"\n📁 {category}:")
            for item in items:
                path = item['path']
                relative_path = path.relative_to(self.script_dir)
                
                if item['type'] == 'directory':
                    size = item.get('size', 0)
                    file_count = item.get('file_count', 0)
                    total_size += size
                    total_files += file_count
                    
                    if 'scan_error' in item:
                        print(f"   📂 {relative_path}/ (scan error: {item['scan_error']})")
                    else:
                        print(f"   📂 {relative_path}/ ({file_count} files, {self.format_size(size)})")
                elif item['type'] == 'symlink':
                    try:
                        target = path.readlink()
                        print(f"   🔗 {relative_path} -> {target}")
                    except:
                        print(f"   🔗 {relative_path} (broken symlink)")
                else:
                    try:
                        size = path.stat().st_size
                        total_size += size
                        total_files += 1
                        print(f"   📄 {relative_path} ({self.format_size(size)})")
                    except:
                        print(f"   📄 {relative_path}")
        
        print(f"\n📊 SUMMARY:")
        print(f"   Total items: {len(self.items_to_remove)}")
        print(f"   Total files: {total_files}")
        print(f"   Total size: {self.format_size(total_size)}")
        
        # Check for environment variables
        env_vars_set = [var for var in self.env_vars_to_clean if os.environ.get(var)]
        if env_vars_set:
            print(f"\n🌍 Environment Variables to clean:")
            for var in env_vars_set:
                print(f"   {var}={os.environ.get(var)}")
        
        print(f"\n{'='*60}")
        return True

    def confirm_cleanup(self):
        """Ask user for confirmation before cleanup"""
        if self.force:
            return True
        
        if self.dry_run:
            print("\n🔍 DRY RUN MODE - No files will actually be deleted")
            return True
        
        print(f"\n⚠️  WARNING: This will permanently delete the items listed above!")
        print(f"This action cannot be undone.")
        
        while True:
            response = input(f"\nProceed with cleanup? [y/N]: ").strip().lower()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no', '']:
                return False
            else:
                print("Please enter 'y' for yes or 'n' for no.")

    def remove_item(self, item):
        """Remove a single item (file, symlink, or directory)"""
        path = item['path']
        item_type = item['type']
        relative_path = path.relative_to(self.script_dir)
        
        if self.dry_run:
            self.log_message(f"Would remove {item_type}: {relative_path}")
            return True
        
        try:
            if item_type == 'directory':
                shutil.rmtree(path)
                self.log_message(f"Removed directory: {relative_path}")
            else:
                path.unlink()
                self.log_message(f"Removed {item_type}: {relative_path}")
            
            self.removed_count += 1
            return True
            
        except FileNotFoundError:
            self.log_message(f"Already removed: {relative_path}", "WARNING")
            return True
        except PermissionError as e:
            self.log_error(f"Permission denied removing {relative_path}", e)
            return False
        except Exception as e:
            self.log_error(f"Failed to remove {relative_path}", e)
            return False

    def clean_environment_variables(self):
        """Clean up environment variables"""
        if not self.env_vars_to_clean:
            return
        
        self.log_message("Cleaning environment variables...")
        
        for var in self.env_vars_to_clean:
            if var in os.environ:
                if self.dry_run:
                    self.log_message(f"Would remove environment variable: {var}")
                else:
                    del os.environ[var]
                    self.log_message(f"Removed environment variable: {var}")

    def perform_cleanup(self):
        """Perform the actual cleanup"""
        if self.dry_run:
            self.log_message("Starting DRY RUN cleanup...", "INFO")
        else:
            self.log_message("Starting cleanup...", "INFO")
        
        success_count = 0
        
        # Remove items by category for better organization
        categories = {}
        for item in self.items_to_remove:
            category = item['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(item)
        
        for category, items in categories.items():
            self.log_message(f"\nCleaning {category}...")
            for item in items:
                if self.remove_item(item):
                    success_count += 1
        
        # Clean environment variables
        self.clean_environment_variables()
        
        return success_count

    def run_cleanup(self):
        """Main cleanup process"""
        print("🧹 Development Files Cleanup Utility")
        print(f"Working directory: {self.script_dir}")
        
        if self.dry_run:
            print("🔍 DRY RUN MODE - No files will be deleted")
        
        # Scan for items to clean
        self.scan_for_cleanup_items()
        
        # Display cleanup plan
        if not self.display_cleanup_plan():
            return True  # Nothing to clean
        
        # Get confirmation
        if not self.confirm_cleanup():
            self.log_message("Cleanup cancelled by user.", "WARNING")
            return False
        
        # Perform cleanup
        success_count = self.perform_cleanup()
        
        # Display results
        print(f"\n{'='*60}")
        print("CLEANUP RESULTS")
        print(f"{'='*60}")
        
        if self.dry_run:
            self.log_message(f"DRY RUN: Would have processed {len(self.items_to_remove)} items", "INFO")
        else:
            self.log_message(f"Successfully processed: {success_count}/{len(self.items_to_remove)} items", "SUCCESS")
            
            if self.errors:
                self.log_message(f"Errors encountered: {len(self.errors)}", "ERROR")
                for error in self.errors:
                    self.log_message(f"  - {error}", "ERROR")
            else:
                self.log_message("No errors encountered!", "SUCCESS")
        
        if not self.dry_run and success_count > 0:
            print(f"\n✨ Repository has been cleaned and is ready for the simple server approach!")
            print(f"You can now run: python3 run_simple_server.py")
        
        return len(self.errors) == 0


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Clean up development files created by the complex local setup",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 cleanup_dev_files.py                    # Interactive cleanup
  python3 cleanup_dev_files.py --dry-run          # Show what would be deleted
  python3 cleanup_dev_files.py --force            # Skip confirmation prompts
  python3 cleanup_dev_files.py --dry-run --verbose # Detailed dry run
        """
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be deleted without actually deleting'
    )
    
    parser.add_argument(
        '--force',
        action='store_true',
        help='Skip confirmation prompts'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Show detailed output'
    )
    
    args = parser.parse_args()
    
    try:
        cleaner = DevFilesCleaner(
            dry_run=args.dry_run,
            force=args.force,
            verbose=args.verbose
        )
        
        success = cleaner.run_cleanup()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Cleanup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()