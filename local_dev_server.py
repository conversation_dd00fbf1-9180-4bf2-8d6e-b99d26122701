#!/usr/bin/env python3
"""
Slicer Local Development Server

This server allows you to run all slicer_wsgi_*.py modules locally for development.
It uses Python's wsgiref.simple_server to serve the WSGI applications.
"""

import os
import sys
import importlib
import traceback
from wsgiref.simple_server import make_server
from urllib.parse import parse_qs

# Add current directory to Python path for local modules
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Import local modules
from dev import login
from dev import organization

class SlicerWSGIDispatcher:
    """WSGI dispatcher that routes requests to appropriate slicer_wsgi modules"""

    def __init__(self):
        self.wsgi_modules = {}
        self.load_wsgi_modules()

    def load_wsgi_modules(self):
        """Load all slicer_wsgi_*.py modules"""
        print("Loading WSGI modules...")

        # Find all slicer_wsgi_*.py files
        for filename in os.listdir('.'):
            if filename.startswith('slicer_wsgi_') and filename.endswith('.py'):
                module_name = filename[:-3]  # Remove .py extension
                service_name = module_name.replace('slicer_wsgi_', '')

                # Skip settings files - they're not WSGI applications
                if service_name.startswith('settings_'):
                    continue

                try:
                    # Import the module
                    module = importlib.import_module(module_name)

                    # Check if it has an application function
                    if hasattr(module, 'application'):
                        self.wsgi_modules[service_name] = module
                        print(f"  Loaded: /{service_name} -> {module_name}")
                    else:
                        print(f"  Skipped: {module_name} (no application function)")

                except Exception as e:
                    print(f"  Error loading {module_name}: {e}")

        print(f"Loaded {len(self.wsgi_modules)} WSGI modules")

    def __call__(self, environ, start_response):
        """WSGI application entry point"""
        path = environ.get('PATH_INFO', '').strip('/')
        method = environ.get('REQUEST_METHOD', 'GET')

        # Handle root path
        if not path:
            return self.handle_index(environ, start_response)

        # Handle login/logout
        if path == 'login':
            return self.handle_login(environ, start_response)
        elif path == 'logout':
            return self.handle_logout(environ, start_response)

        # Check if user is logged in for protected routes
        user = login.get_current_user(environ)
        if not user and path not in ['login', 'logout']:
            return self.redirect_to_login(environ, start_response)

        # Route to appropriate WSGI module
        if path in self.wsgi_modules:
            try:
                return self.wsgi_modules[path].application(environ, start_response)
            except Exception as e:
                return self.handle_error(environ, start_response, f"Error in {path}: {e}")

        # Handle unknown paths
        return self.handle_404(environ, start_response)

    def handle_index(self, environ, start_response):
        """Handle root path - show available modules"""
        user = login.get_current_user(environ)

        if not user:
            return self.redirect_to_login(environ, start_response)

        html = f'''
        <h1>Slicer Local Development Server</h1>
        <p>Welcome, {user}!</p>

        <h2>Available Modules</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px;">
        '''

        for service_name in sorted(self.wsgi_modules.keys()):
            html += f'''
            <div style="border: 1px solid #ccc; padding: 10px; border-radius: 5px;">
                <a href="/{service_name}" style="text-decoration: none; color: #007cba;">
                    <strong>{service_name}</strong>
                </a>
            </div>
            '''

        html += '</div>'

        # Wrap with navigation
        html = organization.wrap_page_with_session(environ, html)

        start_response('200 OK', [('Content-Type', 'text/html')])
        return [html.encode('utf-8')]

    def handle_login(self, environ, start_response):
        """Handle login requests"""
        if environ['REQUEST_METHOD'] == 'POST':
            # Process login form
            try:
                request_body_size = int(environ.get('CONTENT_LENGTH', 0))
            except ValueError:
                request_body_size = 0

            request_body = environ['wsgi.input'].read(request_body_size).decode('utf-8')
            form_data = parse_qs(request_body)

            username = form_data.get('username', [''])[0]
            password = form_data.get('password', [''])[0]

            if login.validate_login(username, password):
                # Create session
                session_id = login.create_session(username)

                # Redirect to home with session cookie
                start_response('302 Found', [
                    ('Location', '/'),
                    ('Set-Cookie', f'slicer_session={session_id}; Path=/; HttpOnly')
                ])
                return [b'']
            else:
                # Login failed
                html = login.get_login_form_html()
                html = html.replace('<h2>', '<h2 style="color: red;">Login Failed</h2><h2>')
                start_response('200 OK', [('Content-Type', 'text/html')])
                return [html.encode('utf-8')]

        # Show login form
        html = login.get_login_form_html()
        start_response('200 OK', [('Content-Type', 'text/html')])
        return [html.encode('utf-8')]

    def handle_logout(self, environ, start_response):
        """Handle logout requests"""
        cookies = login.get_cookie_contents_from_environ(environ)
        session_id = cookies.get('slicer_session', '')

        if session_id:
            login.logout_user(session_id)

        # Redirect to login with expired cookie
        start_response('302 Found', [
            ('Location', '/login'),
            ('Set-Cookie', 'slicer_session=; Path=/; HttpOnly; Expires=Thu, 01 Jan 1970 00:00:00 GMT')
        ])
        return [b'']

    def redirect_to_login(self, environ, start_response):
        """Redirect to login page"""
        start_response('302 Found', [('Location', '/login')])
        return [b'']

    def handle_404(self, environ, start_response):
        """Handle 404 errors"""
        path = environ.get('PATH_INFO', '')
        html = f'''
        <h1>404 - Not Found</h1>
        <p>The path "{path}" was not found.</p>
        <p><a href="/">Return to Home</a></p>
        '''
        html = organization.wrap_page_with_session(environ, html)
        start_response('404 Not Found', [('Content-Type', 'text/html')])
        return [html.encode('utf-8')]

    def handle_error(self, environ, start_response, error_msg):
        """Handle application errors"""
        html = f'''
        <h1>Application Error</h1>
        <p>{error_msg}</p>
        <pre>{traceback.format_exc()}</pre>
        <p><a href="/">Return to Home</a></p>
        '''
        html = organization.wrap_page_with_session(environ, html)
        start_response('500 Internal Server Error', [('Content-Type', 'text/html')])
        return [html.encode('utf-8')]

def main():
    """Start the development server"""
    host = 'localhost'
    port = 8000

    print(f"Starting Slicer Local Development Server...")
    print(f"Server will be available at: http://{host}:{port}")
    print(f"Press Ctrl+C to stop the server")

    # Create WSGI application
    app = SlicerWSGIDispatcher()

    # Create and start server
    with make_server(host, port, app) as httpd:
        print(f"Server started on http://{host}:{port}")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == '__main__':
    main()
