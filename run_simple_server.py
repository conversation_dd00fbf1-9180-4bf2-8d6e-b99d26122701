#!/usr/bin/env python3
"""
Simple Launcher for Slicer Local Development Server

This script provides a one-command startup for the local development server.
It sets up the environment, loads stub modules, and starts the server with
appropriate configuration and error handling.

Usage:
    python3 run_simple_server.py [options]

Examples:
    python3 run_simple_server.py                    # Start on localhost:8000
    python3 run_simple_server.py --port 8080        # Start on port 8080
    python3 run_simple_server.py --host 0.0.0.0     # Listen on all interfaces
    python3 run_simple_server.py --debug            # Enable debug mode
    python3 run_simple_server.py --help             # Show help
"""

import sys
import os
import argparse
import signal
import traceback
import glob
from pathlib import Path


def setup_environment():
    """Set up the Python environment for local development"""
    # Ensure current directory is in sys.path
    current_dir = os.getcwd()
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # Add the script directory to path as well
    script_dir = os.path.dirname(os.path.abspath(__file__))
    if script_dir not in sys.path:
        sys.path.insert(0, script_dir)
    
    print(f"Environment setup complete. Working directory: {current_dir}")


def load_stub_modules():
    """Load stub modules to provide dependencies for slicer_wsgi files"""
    try:
        # Try to import and inject stub modules
        import stub_modules
        stub_modules.inject_stubs()
        print("Stub modules loaded successfully")
        return True
    except ImportError:
        print("Warning: stub_modules.py not found. Creating minimal stubs...")
        # Create minimal inline stubs if stub_modules.py doesn't exist
        create_minimal_stubs()
        return True
    except Exception as e:
        print(f"Error loading stub modules: {e}")
        return False


def create_minimal_stubs():
    """Create minimal stub modules inline if stub_modules.py is not available"""
    from types import ModuleType
    
    # Create minimal organization module
    organization = ModuleType('organization')
    organization.get_config = lambda service: {
        'site_title': 'Slicer 2.0 (Local Development)',
        'build': 'dev-local',
        'home_url': 'http://localhost:8000',
        'time_to_remain_valid': 7200,
        'login_authentication': {'authentication_type': 'blind_trust'},
        'user_list': {'developer': {'password': 'dev'}},
    }
    organization.make_all_dirs = lambda config: None
    organization.wrap_page_with_session = lambda environ, html: html
    organization.make_home_url_from_environ = lambda environ: 'http://localhost:8000'
    
    # Create minimal datastore module
    datastore = ModuleType('datastore')
    datastore.all_datastore = lambda: {}
    datastore.trust = lambda: True
    datastore.get_value = lambda key, default=None: default
    datastore.set_value = lambda key, value, who='system': True
    
    # Create minimal permissions module
    permissions = ModuleType('permissions')
    permissions.permission_prefix_allowed = lambda environ, prefix: True
    permissions.make_permissions_cache_from_datastore = lambda ds: {}
    permissions.make_module_permissions_for_user_from_permissions_cache = lambda user, cache, not_allow, modules: {
        'current_user': user or 'developer',
        'to_show_above_login': ['index'],
        'to_show_for_login': ['login'],
        'to_show_below_login': ['datastore', 'dashboard', 'scan', 'upload', 'tasks']
    }
    permissions.get_module_permissions_for_environ = lambda environ: {
        'current_user': 'developer',
        'to_show_above_login': ['index'],
        'to_show_for_login': ['login'],
        'to_show_below_login': ['datastore', 'dashboard', 'scan', 'upload', 'tasks']
    }
    
    # Create minimal login module
    login = ModuleType('login')
    login.get_current_user = lambda environ, refresh=False: 'developer'
    
    # Inject into sys.modules
    sys.modules['organization'] = organization
    sys.modules['datastore'] = datastore
    sys.modules['permissions'] = permissions
    sys.modules['login'] = login
    
    print("Minimal stub modules created")


def check_slicer_files():
    """Check for slicer_wsgi_*.py files in the current directory"""
    pattern = 'slicer_wsgi_*.py'
    files = glob.glob(pattern)
    
    if not files:
        print("ERROR: No slicer_wsgi_*.py files found in current directory!")
        print("Make sure you're running this script from the directory containing the slicer files.")
        print(f"Current directory: {os.getcwd()}")
        return False
    
    print(f"Found {len(files)} slicer_wsgi files:")
    for file in sorted(files):
        service_name = file[12:-3]  # Remove 'slicer_wsgi_' and '.py'
        print(f"  - {service_name}: {file}")
    
    return True


def start_server(host='localhost', port=8000, debug=False):
    """Start the simple local server"""
    try:
        # Import the simple_local_server module
        import simple_local_server
        
        print(f"\nStarting Slicer Local Development Server...")
        print(f"Host: {host}")
        print(f"Port: {port}")
        print(f"Debug: {debug}")
        print(f"URL: http://{host}:{port}")
        
        # Create the server using simple_local_server
        server_app = simple_local_server.create_server_app(debug=debug)
        
        # Start the WSGI server
        from wsgiref.simple_server import make_server
        
        print(f"\nServer starting on http://{host}:{port}")
        print("Press Ctrl+C to stop the server")
        print("-" * 50)
        
        with make_server(host, port, server_app) as httpd:
            httpd.serve_forever()
            
    except ImportError:
        print("ERROR: simple_local_server.py not found!")
        print("Make sure simple_local_server.py is in the current directory.")
        return False
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"ERROR: Port {port} is already in use!")
            print(f"Try a different port with: python3 run_simple_server.py --port {port + 1}")
        else:
            print(f"ERROR: Could not start server: {e}")
        return False
    except Exception as e:
        print(f"ERROR: Unexpected error starting server: {e}")
        if debug:
            traceback.print_exc()
        return False


def setup_signal_handlers():
    """Set up signal handlers for clean shutdown"""
    def signal_handler(signum, frame):
        print("\nReceived shutdown signal. Cleaning up...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='Slicer Local Development Server Launcher',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 run_simple_server.py                    # Start on localhost:8000
  python3 run_simple_server.py --port 8080        # Start on port 8080
  python3 run_simple_server.py --host 0.0.0.0     # Listen on all interfaces
  python3 run_simple_server.py --debug            # Enable debug mode

This server provides a minimal development environment for viewing and testing
the HTML output of slicer_wsgi_*.py files without requiring the full production
infrastructure. Authentication is bypassed and mock data is used.
        """
    )
    
    parser.add_argument(
        '--host',
        default='localhost',
        help='Host to bind to (default: localhost)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8000,
        help='Port to bind to (default: 8000)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode with detailed error messages'
    )
    
    parser.add_argument(
        '--check-only',
        action='store_true',
        help='Only check for slicer files, don\'t start server'
    )
    
    return parser.parse_args()


def print_startup_banner():
    """Print a helpful startup banner"""
    print("=" * 60)
    print("  Slicer Local Development Server Launcher")
    print("=" * 60)
    print()
    print("This is a simplified development server for frontend work.")
    print("Authentication is bypassed and mock data is used.")
    print()


def print_success_message(host, port):
    """Print success message with helpful information"""
    print("\n" + "=" * 60)
    print("  SERVER STARTED SUCCESSFULLY!")
    print("=" * 60)
    print(f"\nServer URL: http://{host}:{port}")
    print("\nAvailable endpoints:")
    print(f"  • http://{host}:{port}/           (index page)")
    print(f"  • http://{host}:{port}/login      (login page)")
    print(f"  • http://{host}:{port}/index      (main index)")
    print(f"  • http://{host}:{port}/<service>  (other services)")
    print("\nDevelopment Notes:")
    print("  • Authentication is bypassed (use any credentials)")
    print("  • All data is mocked for frontend development")
    print("  • Changes to HTML/CSS will be visible on refresh")
    print("  • Check console for any error messages")
    print("\nPress Ctrl+C to stop the server")
    print("=" * 60)


def cleanup_on_exit():
    """Perform cleanup operations on exit"""
    print("\nPerforming cleanup...")
    # Add any cleanup operations here
    print("Cleanup complete. Goodbye!")


def main():
    """Main function"""
    try:
        # Parse command line arguments
        args = parse_arguments()
        
        # Print startup banner
        print_startup_banner()
        
        # Set up signal handlers for clean shutdown
        setup_signal_handlers()
        
        # Set up the Python environment
        setup_environment()
        
        # Check for slicer files
        if not check_slicer_files():
            sys.exit(1)
        
        # If only checking, exit here
        if args.check_only:
            print("\nCheck complete. All slicer files found.")
            sys.exit(0)
        
        # Load stub modules
        if not load_stub_modules():
            print("ERROR: Failed to load stub modules")
            sys.exit(1)
        
        # Print success message before starting
        print_success_message(args.host, args.port)
        
        # Start the server
        success = start_server(
            host=args.host,
            port=args.port,
            debug=args.debug
        )
        
        if not success:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        if '--debug' in sys.argv:
            traceback.print_exc()
        sys.exit(1)
    finally:
        cleanup_on_exit()


if __name__ == '__main__':
    main()