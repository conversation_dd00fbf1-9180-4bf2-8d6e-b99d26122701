# Local development settings for Slicer application
# This file provides configuration for local development server

import os

service = "settings"
version = service + '.local_dev.1.0'

def get():
    """
    Return local development configuration settings
    """
    # Get current working directory for local development
    current_dir = os.getcwd()
    dev_data_dir = os.path.join(current_dir, 'dev_data')
    
    # Ensure dev_data directory exists
    os.makedirs(dev_data_dir, exist_ok=True)
    os.makedirs(os.path.join(dev_data_dir, 'log', 'slicer'), exist_ok=True)
    os.makedirs(os.path.join(dev_data_dir, 'shm'), exist_ok=True)
    os.makedirs(os.path.join(dev_data_dir, 'www', 'slicer'), exist_ok=True)
    
    return_value = {
        'home_url': 'http://localhost:8000',
        'trust_list': ['developer', 'admin', 'test_user'],
        'data_drop_base': os.path.join(dev_data_dir, 'www', 'slicer', 'log', 'slicer') + '/',
        'ram_disk_path': os.path.join(dev_data_dir, 'shm') + '/',
        'datastore_save_path': os.path.join(dev_data_dir, 'log', 'slicer') + '/',
        'days_to_keep': 4,
        'sites_to_drop': {},
        'days_to_keep_dashboard_data': 120,
        'site_title': 'Slicer Local Development',
    }

    # Local development authentication - simplified for testing
    return_value['login_authentication'] = {
        'authentication_type': 'local_dev',  # Custom type for development
    }

    # User list for local development
    return_value['user_list'] = {
        'developer': {
            'password': 'dev123',
            'admin': True,
            'permissions': ['development_', 'admin_', 'user_']
        },
        'admin': {
            'password': 'admin123', 
            'admin': True,
            'permissions': ['admin_', 'user_']
        },
        'test_user': {
            'password': 'test123',
            'admin': False,
            'permissions': ['user_']
        }
    }

    # Apache settings for local development
    files_path = os.path.join(dev_data_dir, 'www', 'htmlfiles')
    os.makedirs(files_path, exist_ok=True)
    return_value['apache_files_path'] = files_path

    return return_value
