#!/usr/bin/env python3
"""
Slicer Development Server Manager
Provides easy commands to manage the local development server
"""

import os
import sys
import subprocess
import signal
import time

def show_help():
    """Show help information"""
    print("""
Slicer Development Server Manager

Usage: python3 dev_server_manager.py <command>

Commands:
  start     - Start the development server
  stop      - Stop the development server (if running)
  restart   - Restart the development server
  test      - Run tests to verify setup
  status    - Check if server is running
  clean     - Clean development data
  help      - Show this help message

Examples:
  python3 dev_server_manager.py start
  python3 dev_server_manager.py test
  python3 dev_server_manager.py clean
""")

def run_tests():
    """Run the test suite"""
    print("Running development server tests...")
    try:
        result = subprocess.run([sys.executable, 'test_dev_server.py'], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"Error running tests: {e}")
        return False

def start_server():
    """Start the development server"""
    print("Starting Slicer Local Development Server...")
    
    # Check if server is already running
    if is_server_running():
        print("Server is already running on http://localhost:8000")
        return
    
    try:
        # Start the server
        subprocess.run([sys.executable, 'local_dev_server.py'])
    except KeyboardInterrupt:
        print("\nServer stopped by user.")
    except Exception as e:
        print(f"Error starting server: {e}")

def stop_server():
    """Stop the development server"""
    print("Stopping development server...")
    
    try:
        # Find and kill processes using port 8000
        result = subprocess.run(['lsof', '-ti:8000'], 
                              capture_output=True, text=True)
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                try:
                    os.kill(int(pid), signal.SIGTERM)
                    print(f"Stopped process {pid}")
                except:
                    pass
            print("Server stopped.")
        else:
            print("No server process found on port 8000.")
    except Exception as e:
        print(f"Error stopping server: {e}")

def restart_server():
    """Restart the development server"""
    print("Restarting development server...")
    stop_server()
    time.sleep(2)
    start_server()

def is_server_running():
    """Check if server is running on port 8000"""
    try:
        result = subprocess.run(['lsof', '-ti:8000'], 
                              capture_output=True, text=True)
        return bool(result.stdout.strip())
    except:
        return False

def show_status():
    """Show server status"""
    if is_server_running():
        print("✅ Development server is running on http://localhost:8000")
    else:
        print("❌ Development server is not running")
    
    # Check if required files exist
    required_files = [
        'local_dev_server.py',
        'local_dev_settings.py', 
        'organization.py',
        'permissions.py',
        'datastore.py',
        'login.py'
    ]
    
    print("\nRequired files:")
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (missing)")

def clean_data():
    """Clean development data"""
    print("Cleaning development data...")
    
    import shutil
    
    # Remove dev_data directory
    if os.path.exists('dev_data'):
        try:
            shutil.rmtree('dev_data')
            print("  ✅ Removed dev_data directory")
        except Exception as e:
            print(f"  ❌ Error removing dev_data: {e}")
    else:
        print("  ℹ️  dev_data directory doesn't exist")
    
    # Remove cache files
    cache_files = ['local_datastore.json', 'local_sessions.json']
    for file in cache_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"  ✅ Removed {file}")
            except Exception as e:
                print(f"  ❌ Error removing {file}: {e}")
    
    print("Development data cleaned.")

def main():
    """Main entry point"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    if command == 'start':
        start_server()
    elif command == 'stop':
        stop_server()
    elif command == 'restart':
        restart_server()
    elif command == 'test':
        if run_tests():
            print("\n✅ All tests passed! Server is ready to use.")
        else:
            print("\n❌ Some tests failed. Please check the output above.")
    elif command == 'status':
        show_status()
    elif command == 'clean':
        clean_data()
    elif command == 'help':
        show_help()
    else:
        print(f"Unknown command: {command}")
        show_help()

if __name__ == '__main__':
    main()
