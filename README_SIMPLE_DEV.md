# Slicer 2.0 - Simple Local Development Setup

A lightweight, frontend-focused development environment for Slicer 2.0 WSGI applications.

## Quick Start

**One-command setup:**

```bash
python3 run_simple_server.py
```

That's it! The server will start on `http://localhost:8000` and automatically discover all `slicer_wsgi_*.py` files in your current directory.

### Alternative Options

```bash
# Start on a different port
python3 run_simple_server.py --port 8080

# Listen on all network interfaces
python3 run_simple_server.py --host 0.0.0.0

# Enable debug mode for detailed error messages
python3 run_simple_server.py --debug

# Check for slicer files without starting server
python3 run_simple_server.py --check-only

# Show help
python3 run_simple_server.py --help
```

## What This Approach Does Differently

This is a **minimal development server** designed specifically for **frontend development work**. Unlike the complex production-like setup, this approach:

- ✅ **Bypasses production complexity** - No symbolic links, no production paths, no complex dependencies
- ✅ **Focuses on HTML/CSS development** - Perfect for redesigning interfaces and testing layouts
- ✅ **Works out of the box** - No setup scripts, no configuration files, no external dependencies
- ✅ **Uses mock data** - Provides realistic-looking data for frontend testing without database setup
- ✅ **Automatic discovery** - Finds and serves all `slicer_wsgi_*.py` files automatically
- ✅ **Simple authentication** - Bypasses complex authentication for easy testing

**This is NOT a production replica** - it's a frontend development tool.

## URL Mapping

The server automatically maps URLs to your `slicer_wsgi_*.py` files:

| URL | File | Purpose |
|-----|------|---------|
| `/` | Index page | Lists all available applications |
| `/login` | `slicer_wsgi_login.py` | Login interface |
| `/index` | `slicer_wsgi_index.py` | Main dashboard/index |
| `/datastore` | `slicer_wsgi_datastore.py` | Data management interface |
| `/dashboard` | `slicer_wsgi_dashboard.py` | System dashboard |
| `/scan` | `slicer_wsgi_scan.py` | Scanning interface |
| `/upload` | `slicer_wsgi_upload.py` | File upload interface |
| `/tasks` | `slicer_wsgi_tasks.py` | Task management |
| `/rings` | `slicer_wsgi_rings.py` | Ring management |
| `/certificates` | `slicer_wsgi_certificates.py` | Certificate management |
| `/watchdog` | `slicer_wsgi_watchdog.py` | System monitoring |
| `/devicecommand` | `slicer_wsgi_devicecommand.py` | Device commands |

**Note:** Only files that actually exist in your directory will be available.

## Limitations (What's Mocked vs. Real)

### ✅ Real (Works as Expected)
- **HTML rendering** - All HTML output is generated normally
- **CSS styling** - All stylesheets work as intended
- **JavaScript** - Client-side scripts function normally
- **Form submissions** - Forms can be submitted (though data isn't persisted)
- **URL routing** - Navigation between pages works correctly

### ⚠️ Mocked (Simplified for Development)
- **Authentication** - Login always succeeds with any credentials
- **Database operations** - All data is temporary and in-memory
- **File uploads** - Files are accepted but not permanently stored
- **External API calls** - May fail or return mock responses
- **Production paths** - Uses temporary directories instead of `/var/www/html/`
- **User permissions** - All users have full access to all modules
- **Session persistence** - Sessions reset when server restarts

### ❌ Not Available
- **Real data persistence** - Changes don't survive server restarts
- **Production integrations** - External systems are not connected
- **Complex authentication flows** - LDAP, SSO, etc. are bypassed
- **Background tasks** - Scheduled jobs and background processes don't run

## Frontend Development Workflow

### 1. Start the Server
```bash
python3 run_simple_server.py
```

### 2. Open Your Browser
Visit `http://localhost:8000` to see the index page with all available applications.

### 3. Navigate to Your Target Page
Click on the application you want to work on, or go directly to its URL (e.g., `http://localhost:8000/login`).

### 4. Make Changes
Edit the HTML/CSS in your `slicer_wsgi_*.py` files:

```python
# In slicer_wsgi_login.py, modify the HTML:
html = """
<html>
<head>
    <title>Login - Slicer 2.0</title>
    <style>
        /* Add your custom CSS here */
        .login-form { 
            max-width: 400px; 
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="login-form">
        <!-- Your HTML changes here -->
    </div>
</body>
</html>
"""
```

### 5. Refresh to See Changes
Simply refresh your browser to see the changes immediately. No server restart needed!

### 6. Test Different Scenarios
Use the mock data and authentication to test various UI states:
- Logged in vs. logged out views
- Different user permission levels
- Various data states (empty, full, error conditions)

## Mock Data Explanation

The server provides realistic mock data for frontend testing:

### User Data
```python
# Available test users (all passwords work):
users = {
    'developer': {'password': 'dev'},
    'admin': {'password': 'admin'}
}
```

### Configuration Data
```python
config = {
    'site_title': 'Slicer 2.0 (Local Development)',
    'build': 'dev-local',
    'time_to_remain_valid': 7200,  # 2 hours
}
```

### Permissions
- All users have access to all modules
- No permission restrictions are enforced
- Module lists are populated with realistic entries

### Modifying Mock Data
To test different scenarios, edit the mock data in `simple_local_server.py`:

```python
# In create_stub_modules() function:
mock_config = {
    'site_title': 'Your Custom Title',
    # Add more test data here
}
```

## Troubleshooting

### Server Won't Start

**Problem:** `ERROR: No slicer_wsgi_*.py files found`
```
Solution: Make sure you're running the command from the directory containing your slicer_wsgi_*.py files.
```

**Problem:** `ERROR: Port 8000 is already in use`
```bash
# Try a different port:
python3 run_simple_server.py --port 8080
```

**Problem:** `ImportError` or module not found errors
```bash
# Enable debug mode to see detailed errors:
python3 run_simple_server.py --debug
```

### Page Doesn't Load Correctly

**Problem:** Blank page or error 500
1. Check the console output for error messages
2. Enable debug mode: `python3 run_simple_server.py --debug`
3. Look for missing imports or syntax errors in your slicer_wsgi files

**Problem:** CSS/styling issues
1. Check that your CSS is embedded in the HTML or properly linked
2. Use browser developer tools to inspect CSS loading
3. Verify file paths are correct for any external resources

### Authentication Issues

**Problem:** Can't log in
```
Solution: Use any username/password combination - authentication is bypassed in development mode.
Try: username="developer", password="dev"
```

**Problem:** Permission denied errors
```
Solution: All permissions are granted in development mode. If you see permission errors, 
they're likely coming from the original code logic, not the mock system.
```

### Changes Not Visible

**Problem:** Modified HTML/CSS doesn't appear
1. Hard refresh your browser (Ctrl+F5 or Cmd+Shift+R)
2. Check browser cache settings
3. Verify you're editing the correct file
4. Check console for any Python syntax errors

## Comparison: Simple vs. Complex Setup

### Use the Simple Setup When:
- ✅ **Frontend development** - Redesigning interfaces, testing layouts
- ✅ **Quick prototyping** - Testing new UI concepts
- ✅ **CSS/HTML work** - Styling and responsive design
- ✅ **Visual testing** - Checking how pages look with different data
- ✅ **New developer onboarding** - Getting familiar with the codebase
- ✅ **Demo preparation** - Creating screenshots or demos

### Use the Complex Setup When:
- ⚙️ **Backend development** - Working on business logic, data processing
- ⚙️ **Integration testing** - Testing with real databases and external systems
- ⚙️ **Authentication development** - Working on login/permission systems
- ⚙️ **Production debugging** - Reproducing production-specific issues
- ⚙️ **Full system testing** - End-to-end testing with real data flows

### Migration Between Setups

**From Complex to Simple:**
```bash
# Clean up the complex setup first:
python3 cleanup_dev_files.py

# Then start the simple server:
python3 run_simple_server.py
```

**From Simple to Complex:**
The simple setup doesn't create any files that conflict with the complex setup. You can switch at any time by running the appropriate setup script.

## Browser Testing Tips

### Developer Tools
1. **Open Developer Tools** (F12) to inspect HTML structure and CSS
2. **Use the Console** to check for JavaScript errors
3. **Network Tab** to see which resources are loading
4. **Responsive Design Mode** to test mobile layouts

### Testing Different Scenarios
```bash
# Test with different users by visiting:
http://localhost:8000/login

# Test different page states by modifying mock data
# Test responsive design by resizing browser window
# Test form submissions by filling out forms
```

### Performance Testing
- The simple server is not optimized for performance
- Use it for visual testing, not performance benchmarking
- For performance testing, use the complex setup or production environment

### Cross-Browser Testing
- Test in multiple browsers (Chrome, Firefox, Safari, Edge)
- Check for CSS compatibility issues
- Verify JavaScript functionality across browsers

## Advanced Usage

### Custom Port and Host
```bash
# For team development (accessible to other machines):
python3 run_simple_server.py --host 0.0.0.0 --port 8000

# Then team members can access: http://your-ip:8000
```

### Debug Mode
```bash
# Get detailed error messages and stack traces:
python3 run_simple_server.py --debug
```

### Integration with IDEs
- Most IDEs can be configured to run the server as an external tool
- Set up file watchers to automatically refresh browsers on changes
- Use IDE debugging features with the `--debug` flag

## Getting Help

### Common Resources
- Check the console output for error messages
- Use `--debug` flag for detailed error information
- Inspect browser developer tools for frontend issues

### File Structure
```
your-project/
├── slicer_wsgi_*.py          # Your WSGI applications
├── run_simple_server.py      # Server launcher
├── simple_local_server.py    # Server implementation
├── stub_modules.py           # Mock module implementations
└── README_SIMPLE_DEV.md      # This documentation
```

### Support
This simple development setup is designed to be self-contained and easy to debug. Most issues can be resolved by:
1. Checking console output
2. Using debug mode
3. Verifying file locations
4. Checking browser developer tools

---

**Happy frontend development!** 🎨

This simple setup gets you up and running quickly so you can focus on creating great user interfaces without getting bogged down in production complexity.