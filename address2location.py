#!/usr/bin/env python3
"""
Root-level address2location module that imports from dev
This allows WSGI modules to import address2location while using the dev version
"""

# Import everything from the dev version
try:
    from dev.address2location import *
except ImportError:
    # Fallback mock functions if dev version not available
    def get_location_from_address(address):
        """Mock function to get location from address"""
        return {
            'latitude': 0.0,
            'longitude': 0.0,
            'address': address,
            'status': 'mock_data'
        }

    def get_address_from_location(latitude, longitude):
        """Mock function to get address from coordinates"""
        return {
            'address': f'Mock Address at {latitude}, {longitude}',
            'latitude': latitude,
            'longitude': longitude,
            'status': 'mock_data'
        }

    def initialize():
        """Mock initialization function"""
        pass

    def cleanup():
        """Mock cleanup function"""
        pass
