#!/usr/bin/env python3
"""
Root-level reports module that imports from dev
This allows WSGI modules to import reports while using the dev version
"""

# Import everything from the dev version
try:
    from dev.reports import *
except ImportError:
    # Fallback mock functions if dev version not available
    def get_top_level_issues(siteid=''):
        """Mock function to get top level issues"""
        return {
            'issues': [
                {
                    'id': 'mock_issue_1',
                    'title': 'Mock Issue 1',
                    'status': 'open',
                    'priority': 'medium',
                    'siteid': siteid
                },
                {
                    'id': 'mock_issue_2', 
                    'title': 'Mock Issue 2',
                    'status': 'resolved',
                    'priority': 'low',
                    'siteid': siteid
                }
            ],
            'count': 2,
            'siteid': siteid
        }

    def get_reportables(issues, siteid=''):
        """Mock function to get reportable data from issues"""
        return {
            'headers': ['ID', 'Title', 'Status', 'Priority'],
            'rows': [
                ['mock_issue_1', 'Mock Issue 1', 'open', 'medium'],
                ['mock_issue_2', 'Mock Issue 2', 'resolved', 'low']
            ],
            'links': [
                '/issue/mock_issue_1',
                '/issue/mock_issue_2'
            ],
            'color': ['yellow', 'green']
        }

    def get_report_data(report_type='default', siteid=''):
        """Mock function to get report data"""
        return {
            'type': report_type,
            'siteid': siteid,
            'data': get_reportables(get_top_level_issues(siteid)['issues'], siteid),
            'timestamp': '2025-01-20 00:00:00',
            'status': 'mock_data'
        }

    def generate_report(report_type='default', siteid=''):
        """Mock function to generate a report"""
        return get_report_data(report_type, siteid)

    def initialize():
        """Mock initialization function"""
        pass

    def cleanup():
        """Mock cleanup function"""
        pass
