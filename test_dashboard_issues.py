#!/usr/bin/env python3
"""
Comprehensive test to identify all dashboard issues
"""

import sys
import os

def test_dashboard_comprehensive():
    """Test dashboard and identify all issues"""
    print("=== Comprehensive Dashboard Issue Analysis ===")
    
    try:
        import local_dev_server
        from dev import login
        
        # Create dispatcher
        dispatcher = local_dev_server.SlicerWSGIDispatcher()
        
        # Create session for developer user
        session_id = login.create_session('developer')
        
        # Test different scenarios
        test_scenarios = [
            {
                'name': 'Basic GET request',
                'environ': {
                    'REQUEST_METHOD': 'GET',
                    'PATH_INFO': '/dashboard',
                    'REMOTE_ADDR': '127.0.0.1',
                    'HTTP_COOKIE': f'slicer_session={session_id}',
                    'QUERY_STRING': '',
                    'SERVER_NAME': 'localhost',
                    'SERVER_PORT': '8000'
                }
            },
            {
                'name': 'GET with data request',
                'environ': {
                    'REQUEST_METHOD': 'GET',
                    'PATH_INFO': '/dashboard',
                    'REMOTE_ADDR': '127.0.0.1',
                    'HTTP_COOKIE': f'slicer_session={session_id}',
                    'QUERY_STRING': 'get=data&type=issues',
                    'SERVER_NAME': 'localhost',
                    'SERVER_PORT': '8000'
                }
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n--- Testing: {scenario['name']} ---")
            
            try:
                responses = []
                def start_response(status, headers):
                    responses.append((status, headers))
                
                result = dispatcher(scenario['environ'], start_response)
                
                if responses:
                    status, headers = responses[0]
                    print(f"Status: {status}")
                    
                    if status.startswith('200'):
                        body = b''.join(result).decode('utf-8')
                        print(f"Response length: {len(body)} characters")
                        
                        # Analyze the response content
                        if 'Traceback' in body:
                            print("❌ Contains Python traceback")
                            # Extract the error details
                            lines = body.split('<br>')
                            for i, line in enumerate(lines):
                                if 'Traceback' in line:
                                    print(f"   Error context:")
                                    for j in range(i, min(i+5, len(lines))):
                                        clean_line = lines[j].replace('<br>', '').strip()
                                        if clean_line:
                                            print(f"   {clean_line}")
                                    break
                        
                        elif 'exception' in body.lower():
                            print("❌ Contains exception content")
                            print(f"   First 300 chars: {body[:300]}")
                        
                        elif 'error' in body.lower():
                            print("⚠️  Contains error content")
                            print(f"   First 300 chars: {body[:300]}")
                        
                        elif len(body) < 100:
                            print("⚠️  Very short response")
                            print(f"   Full content: {body}")
                        
                        else:
                            print("✅ Appears to be valid dashboard content")
                            print(f"   First 200 chars: {body[:200]}")
                            
                    else:
                        print(f"❌ Unexpected status: {status}")
                else:
                    print("❌ No response received")
                    
            except Exception as e:
                print(f"❌ Test failed with error: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dashboard_direct_import():
    """Test dashboard module import directly"""
    print("\n=== Dashboard Module Import Test ===")
    
    try:
        # Clear any cached imports
        if 'slicer_wsgi_dashboard' in sys.modules:
            del sys.modules['slicer_wsgi_dashboard']
        
        import slicer_wsgi_dashboard
        print("✅ Dashboard module imported successfully")
        
        # Check for startup exceptions
        if hasattr(slicer_wsgi_dashboard, 'startup_exceptions'):
            if slicer_wsgi_dashboard.startup_exceptions:
                print("⚠️  Startup exceptions found:")
                print(slicer_wsgi_dashboard.startup_exceptions)
            else:
                print("✅ No startup exceptions")
        
        # Check critical variables
        critical_vars = [
            'sites_to_drop',
            'service_config',
            'dashboard_raw_root',
            's_stash_report_file',
            'days_to_keep_dashboard_data'
        ]
        
        for var in critical_vars:
            if hasattr(slicer_wsgi_dashboard, var):
                value = getattr(slicer_wsgi_dashboard, var)
                print(f"✅ {var}: {type(value).__name__} = {value}")
            else:
                print(f"❌ {var}: NOT DEFINED")
        
        return True
        
    except Exception as e:
        print(f"❌ Dashboard import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all dashboard tests"""
    print("=" * 60)
    print("Dashboard Issue Analysis")
    print("=" * 60)
    
    tests = [
        test_dashboard_direct_import,
        test_dashboard_comprehensive
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Dashboard Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Dashboard is working correctly!")
    else:
        print("❌ Dashboard has issues that need to be fixed.")
        print("\nNext steps:")
        print("1. Fix any missing variable definitions")
        print("2. Handle missing configuration gracefully")
        print("3. Ensure all required modules are available in dev environment")
    
    return 0 if passed == total else 1

if __name__ == '__main__':
    sys.exit(main())
